#include "pch.h"
#include "TrialManager.hpp"
#include "halvault_error.h"
#include "hwid.hpp"
#include "tools.h"
#include "UniqueHandle.h"
#include <ScopeGuard.h>

#include <windows.h>
#include <shlobj.h>
#include <strsafe.h>
#include <string>
#include <vector>
#include <array>
#include <mutex>
#include <atomic>
#include <fstream>

// Crypto++
#include <cryptopp/gcm.h>
#include <cryptopp/aes.h>
#include <cryptopp/sha.h>
#include <cryptopp/md5.h>
#include <cryptopp/osrng.h>
#include <cryptopp/filters.h>

// VMP 宏可能在工程中已定义
#ifndef VMP_GUARD
#define VMP_GUARD(x)
#endif

namespace {

struct TrialDoc {
    uint8_t  version{1};
    uint8_t  mode{0};            // 0=Days,1=Runs
    uint16_t reserved{0};
    uint32_t limit{0};           // 天数或次数
    uint32_t installUnix{0};
    uint32_t lastSeenUnix{0};
    uint32_t runsUsed{0};        // Runs 模式有效
    uint32_t seqNo{0};           // 防回滚
};

static std::mutex              g_mutex;
static std::string             g_softwareId;
static std::wstring            g_md5Lower;      // 32位大写来自 Tools::MD5OfFile，故此处自算
static TrialMode               g_mode = TrialMode::Days;
static uint32_t                g_limit = 0;
static bool                    g_inited = false;

static bool ToLowerHex(const std::wstring& upperHex, std::wstring& lowerHex) {
    lowerHex.clear(); lowerHex.reserve(upperHex.size());
    for (wchar_t c : upperHex) lowerHex.push_back((wchar_t)towlower(c));
    return true;
}

static std::wstring CalcSoftwareIdMd5Lower(const std::string& sid) {
    // 复用 Crypto++ MD5 计算字符串 MD5
    CryptoPP::Weak1::MD5 md5;
    md5.Update(reinterpret_cast<const CryptoPP::byte*>(sid.data()), (size_t)sid.size());
    CryptoPP::byte d[CryptoPP::Weak1::MD5::DIGESTSIZE]{};
    md5.Final(d);
    static const wchar_t* hex = L"0123456789abcdef";
    std::wstring out; out.resize(32);
    for (int i=0;i<16;++i){ out[i*2+0]=hex[(d[i]>>4)&0xF]; out[i*2+1]=hex[d[i]&0xF]; }
    return out;
}

static std::wstring GetProgramDataPath() {
    PWSTR path = nullptr;
    if (SHGetKnownFolderPath(FOLDERID_ProgramData, 0, nullptr, &path) == S_OK) {
        std::wstring p(path); CoTaskMemFree(path); return p;
    }
    return L"C:\\ProgramData"; // 回退
}

static std::wstring GetLocalAppDataPath() {
    PWSTR path = nullptr;
    if (SHGetKnownFolderPath(FOLDERID_LocalAppData, 0, nullptr, &path) == S_OK) {
        std::wstring p(path); CoTaskMemFree(path); return p;
    }
    // Win7 回退
    wchar_t buf[MAX_PATH]{};
    if (SUCCEEDED(SHGetFolderPathW(nullptr, CSIDL_LOCAL_APPDATA, nullptr, SHGFP_TYPE_CURRENT, buf))) {
        return std::wstring(buf);
    }
    // 最终回退到用户目录下 .cache
    wchar_t home[MAX_PATH]{}; DWORD n=GetEnvironmentVariableW(L"USERPROFILE", home, MAX_PATH);
    if (n>0 && n<MAX_PATH) return std::wstring(home) + L"\\.cache";
    return L"C:"; // 保底
}

static bool DirCreateRecursive(const std::wstring& dir) {
    if (dir.empty()) return false;
    if (::CreateDirectoryW(dir.c_str(), nullptr)) return true;
    DWORD e = GetLastError();
    if (e == ERROR_ALREADY_EXISTS) return true;
    size_t pos = dir.find_last_of(L"\\/");
    if (pos == std::wstring::npos) return false;
    std::wstring parent = dir.substr(0, pos);
    if (!DirCreateRecursive(parent)) return false;
    return ::CreateDirectoryW(dir.c_str(), nullptr) || GetLastError()==ERROR_ALREADY_EXISTS;
}

static std::wstring FilePathFor(const std::wstring& md5Lower) {
    std::wstring base = GetProgramDataPath();
    base += L"\\" + md5Lower;
    DirCreateRecursive(base);
    return base + L"\\trial.dat";
}

static std::wstring AdsPathForDir(const std::wstring& md5Lower) {
    std::wstring base = GetProgramDataPath();
    base += L"\\" + md5Lower;
    return base + L":trial"; // 目录 ADS
}

static std::wstring LocalAdsPathForDir(const std::wstring& md5Lower) {
    std::wstring base = GetLocalAppDataPath();
    base += L"\\" + md5Lower;
    DirCreateRecursive(base);
    return base + L":trial"; // 目录 ADS（用户可写）
}

static std::wstring RegPathFor(const std::wstring& md5Lower) {
    return L"Software\\HALVault\\" + md5Lower;
}

static void DeriveKey(std::array<uint8_t,32>& keyOut) {
    VMP_GUARD("Trial_DeriveKey");
    auto hw = hwid::GetHWID();
    CryptoPP::SHA256 sha;
    sha.CalculateDigest(keyOut.data(), hw.data(), hw.size());
}

static bool EncryptDoc(const TrialDoc& doc, std::vector<uint8_t>& out) {
    std::array<uint8_t,32> key{}; DeriveKey(key);
    CryptoPP::AutoSeededRandomPool prng;
    std::array<uint8_t,12> iv{}; prng.GenerateBlock(iv.data(), iv.size());

    std::string plain(reinterpret_cast<const char*>(&doc), sizeof(doc));
    CryptoPP::GCM<CryptoPP::AES>::Encryption enc;
    enc.SetKeyWithIV(key.data(), key.size(), iv.data(), iv.size());

    std::string cipher;
    try{
        VMP_BLOCK("Trial_EncryptDoc") {
            CryptoPP::StringSource ss(plain, true,
                new CryptoPP::AuthenticatedEncryptionFilter(enc, new CryptoPP::StringSink(cipher)));
        }
    }catch(...){ return false; }

    out.clear(); out.reserve(4+1+iv.size()+cipher.size());
    out.insert(out.end(), {'T','R','I','A'});
    out.push_back(1); // ver
    out.insert(out.end(), iv.begin(), iv.end());
    out.insert(out.end(), cipher.begin(), cipher.end());
    return true;
}

static bool DecryptDoc(const std::vector<uint8_t>& in, TrialDoc& doc) {
    if (in.size() < 4+1+12+16) return false;
    if (std::memcmp(in.data(), "TRIA", 4)!=0) return false;
    if (in[4] != 1) return false;
    const uint8_t* iv = in.data()+5;
    const uint8_t* c  = in.data()+5+12;
    size_t clen = in.size() - (5+12);

    std::array<uint8_t,32> key{}; DeriveKey(key);
    CryptoPP::GCM<CryptoPP::AES>::Decryption dec;
    dec.SetKeyWithIV(key.data(), key.size(), iv, 12);
    std::string plain;
    try{
        VMP_BLOCK("Trial_DecryptDoc") {
            CryptoPP::StringSource ss(c, clen, true,
                new CryptoPP::AuthenticatedDecryptionFilter(dec, new CryptoPP::StringSink(plain),
                    CryptoPP::AuthenticatedDecryptionFilter::THROW_EXCEPTION));
        }
    }catch(...){ return false; }
    if (plain.size() != sizeof(TrialDoc)) return false;
    std::memcpy(&doc, plain.data(), sizeof(TrialDoc));
    return true;
}

// 文件 I/O（原子写）
static bool WriteFileBlob(const std::wstring& path, const std::vector<uint8_t>& blob) {
    std::wstring dir = path;
    size_t pos = dir.find_last_of(L"\\/"); if (pos!=std::wstring::npos) dir = dir.substr(0,pos);
    DirCreateRecursive(dir);
    std::wstring tmp = path + L".tmp";
    UniqueHandle f(CreateFileW(tmp.c_str(), GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS, FILE_ATTRIBUTE_HIDDEN, nullptr));
    if (!f) return false;
    DWORD wr=0; if (!::WriteFile(f.get(), blob.data(), (DWORD)blob.size(), &wr, nullptr) || wr != blob.size()) return false;
    ::FlushFileBuffers(f.get());
    f.reset();
    return ::MoveFileExW(tmp.c_str(), path.c_str(), MOVEFILE_REPLACE_EXISTING | MOVEFILE_WRITE_THROUGH) != 0;
}
static bool ReadFileBlob(const std::wstring& path, std::vector<uint8_t>& blob) {
    UniqueHandle f(CreateFileW(path.c_str(), GENERIC_READ, FILE_SHARE_READ, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_HIDDEN, nullptr));
    if (!f) return false; DWORD sz = GetFileSize(f.get(), nullptr); if (sz==INVALID_FILE_SIZE) return false;
    blob.resize(sz); DWORD rd=0; if (!::ReadFile(f.get(), blob.data(), sz, &rd, nullptr) || rd!=sz) return false; return true;
}

// 目录 ADS I/O（需 FILE_FLAG_BACKUP_SEMANTICS 才能打开目录数据流）
static bool WriteDirADS(const std::wstring& dirAdsPath, const std::vector<uint8_t>& blob) {
    UniqueHandle f(CreateFileW(dirAdsPath.c_str(), GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS,
                               FILE_FLAG_BACKUP_SEMANTICS, nullptr));
    if (!f) return false; DWORD wr=0; BOOL ok = ::WriteFile(f.get(), blob.data(), (DWORD)blob.size(), &wr, nullptr);
    return ok && wr==(DWORD)blob.size();
}
static bool ReadDirADS(const std::wstring& dirAdsPath, std::vector<uint8_t>& blob) {
    UniqueHandle f(CreateFileW(dirAdsPath.c_str(), GENERIC_READ, FILE_SHARE_READ, nullptr, OPEN_EXISTING,
                               FILE_FLAG_BACKUP_SEMANTICS, nullptr));
    if (!f) return false; DWORD sz = GetFileSize(f.get(), nullptr); if (sz==INVALID_FILE_SIZE) return false;
    blob.resize(sz); DWORD rd=0; BOOL ok=::ReadFile(f.get(), blob.data(), sz, &rd, nullptr); return ok && rd==sz;
}

// 注册表 I/O（HKCU）
static bool WriteRegHKCU(const std::wstring& subKey, const std::vector<uint8_t>& blob) {
    HKEY h = nullptr; if (RegCreateKeyExW(HKEY_CURRENT_USER, subKey.c_str(), 0, nullptr, 0, KEY_WRITE, nullptr, &h, nullptr)) return false;
    LONG rc = RegSetValueExW(h, L"Cache", 0, REG_BINARY, blob.data(), (DWORD)blob.size()); RegCloseKey(h); return rc==ERROR_SUCCESS;
}
static bool ReadRegHKCU(const std::wstring& subKey, std::vector<uint8_t>& blob) {
    HKEY h = nullptr; if (RegOpenKeyExW(HKEY_CURRENT_USER, subKey.c_str(), 0, KEY_READ, &h)) return false;
    DWORD sz=0; if (RegQueryValueExW(h, L"Cache", nullptr, nullptr, nullptr, &sz)) { RegCloseKey(h); return false; }
    blob.resize(sz); LONG rc = RegQueryValueExW(h, L"Cache", nullptr, nullptr, blob.data(), &sz); RegCloseKey(h); return rc==ERROR_SUCCESS;
}

// 读取三副本，选择 seqNo 最大的有效样本
static bool LoadBestDoc(const std::wstring& md5Lower, TrialDoc& outDoc) {
    VMP_GUARD("Trial_LoadBest");
    std::vector<std::vector<uint8_t>> blobs;
    std::vector<uint8_t> b;
    if (ReadFileBlob(FilePathFor(md5Lower), b)) blobs.emplace_back(b);
    b.clear(); if (ReadRegHKCU(RegPathFor(md5Lower), b)) blobs.emplace_back(b);
    b.clear(); if (ReadDirADS(AdsPathForDir(md5Lower), b)) blobs.emplace_back(b);
    b.clear(); if (ReadDirADS(LocalAdsPathForDir(md5Lower), b)) blobs.emplace_back(b);

    TrialDoc best{}; bool have=false; uint32_t bestSeq=0;
    for (auto& enc : blobs) {
        TrialDoc d{}; if (!DecryptDoc(enc, d)) continue;
        if (!have || d.seqNo > bestSeq) { best = d; bestSeq = d.seqNo; have = true; }
    }
    if (!have) return false;
    outDoc = best; return true;
}

static void SaveAllReplicas(const std::wstring& md5Lower, const std::vector<uint8_t>& blob) {
    VMP_GUARD("Trial_SaveAll");
    WriteFileBlob(FilePathFor(md5Lower), blob);
    WriteRegHKCU(RegPathFor(md5Lower), blob);
    WriteDirADS(AdsPathForDir(md5Lower), blob);
    WriteDirADS(LocalAdsPathForDir(md5Lower), blob);
}

} // namespace

extern "C" HALVAULT_API int TrialInitialize(const char* softwareId) noexcept {
    VMP_GUARD("TrialInitialize");
    std::lock_guard<std::mutex> lk(g_mutex);
    if (softwareId == nullptr) { HalSetLastError(HAL_ERR_INVALID_ARG); return HAL_ERR_INVALID_ARG; }
    g_softwareId = softwareId;
    g_md5Lower = CalcSoftwareIdMd5Lower(g_softwareId);

    TrialMode mode; uint32_t limit;
    TrialGetDefaultConfig(softwareId, mode, limit);
   
    g_mode = mode; g_limit = limit; g_inited = true;

    // 如果不存在任何副本则创建一个新的 TrialDoc
    TrialDoc doc{};
    if (!LoadBestDoc(g_md5Lower, doc)) {
        doc.version = 1; doc.mode = (g_mode==TrialMode::Days)?0:1; doc.limit=g_limit; doc.runsUsed=0; doc.seqNo=1;
        doc.installUnix = static_cast<uint32_t>(std::time(nullptr));
        doc.lastSeenUnix = doc.installUnix;
        std::vector<uint8_t> enc; if (!EncryptDoc(doc, enc)) { HalSetLastError(HAL_ERR_CRYPTO); return HAL_ERR_CRYPTO; }
        SaveAllReplicas(g_md5Lower, enc);
    }
    HalSetLastError(HAL_OK); return HAL_OK;
}

extern "C" HALVAULT_API int TrialEvaluate(TrialStatus& out) noexcept {
    VMP_GUARD("TrialEvaluate");
    std::lock_guard<std::mutex> lk(g_mutex);
    if (!g_inited) { HalSetLastError(HAL_ERR_INVALID_ARG); return HAL_ERR_INVALID_ARG; }
    TrialDoc doc{}; if (!LoadBestDoc(g_md5Lower, doc)) { HalSetLastError(HAL_ERR_FILE_NOT_FOUND); return HAL_ERR_FILE_NOT_FOUND; }

    uint32_t now = static_cast<uint32_t>(std::time(nullptr));
    out.nowUnix = now; out.installUnix = doc.installUnix; out.limit = doc.limit; out.mode = (doc.mode==0?TrialMode::Days:TrialMode::Runs); out.runsUsed = doc.runsUsed;

    uint32_t remaining = 0;
    if (doc.mode == 0) {
        uint32_t daysElapsed = (now > doc.installUnix) ? ( (now - doc.installUnix) / 86400u ) : 0u;
        remaining = (doc.limit > daysElapsed) ? (doc.limit - daysElapsed) : 0u;
    } else {
        remaining = (doc.limit > doc.runsUsed) ? (doc.limit - doc.runsUsed) : 0u;
    }
    out.remaining = remaining; out.allowed = (remaining > 0);

    // 轻量更新 lastSeenUnix / seqNo（不改变计数）
    if (now + 300u < doc.lastSeenUnix) {
        // 时间回退，直接判定不允许（保护）
        out.allowed = false; out.remaining = 0;
    }
    doc.lastSeenUnix = now; doc.seqNo++;
    std::vector<uint8_t> enc; if (EncryptDoc(doc, enc)) SaveAllReplicas(g_md5Lower, enc);

    HalSetLastError(HAL_OK); return HAL_OK;
}

extern "C" HALVAULT_API int TrialConsumeOne() noexcept {
    VMP_GUARD("TrialConsumeOne");
    std::lock_guard<std::mutex> lk(g_mutex);
    if (!g_inited) { HalSetLastError(HAL_ERR_INVALID_ARG); return HAL_ERR_INVALID_ARG; }
    TrialDoc doc{}; if (!LoadBestDoc(g_md5Lower, doc)) { HalSetLastError(HAL_ERR_FILE_NOT_FOUND); return HAL_ERR_FILE_NOT_FOUND; }
    if (doc.mode != 1) { HalSetLastError(HAL_ERR_INVALID_ARG); return HAL_ERR_INVALID_ARG; }

    uint32_t now = static_cast<uint32_t>(std::time(nullptr));
    if (now + 300u < doc.lastSeenUnix) { HalSetLastError(HAL_ERR_VERIFY_FAIL); return HAL_ERR_VERIFY_FAIL; }

    if (doc.runsUsed >= doc.limit) { HalSetLastError(HAL_ERR_VERIFY_FAIL); return HAL_ERR_VERIFY_FAIL; }
    doc.runsUsed++; doc.lastSeenUnix = now; doc.seqNo++;
    std::vector<uint8_t> enc; if (!EncryptDoc(doc, enc)) { HalSetLastError(HAL_ERR_CRYPTO); return HAL_ERR_CRYPTO; }
    SaveAllReplicas(g_md5Lower, enc);
    HalSetLastError(HAL_OK); return HAL_OK;
}

extern "C" HALVAULT_API int TrialGetDefaultConfig(const char* softwareId, TrialMode& modeOut, uint32_t& limitOut) noexcept {
    VMP_GUARD("TrialGetDefaultConfig");
    // 缺省策略：按软件 ID（字符串）前缀或特定 ID 走分支；否则返回 Days=14
    if (softwareId == nullptr) { modeOut = TrialMode::Days; limitOut = 14; HalSetLastError(HAL_OK); return HAL_OK; }
    std::string sid(softwareId);
    // 例：VE00013、VE 开头的产品使用 Runs=30，其余 Days=14
    if ("VE00013" == softwareId) { modeOut = TrialMode::Runs; limitOut = 30; HalSetLastError(HAL_OK); return HAL_OK; }
    // 例：以 BDL 开头的磁盘类产品给 Days=7
    if ("BDL00013" == softwareId) { modeOut = TrialMode::Days; limitOut = 7; HalSetLastError(HAL_OK); return HAL_OK; }
    // 默认
    modeOut = TrialMode::Days; limitOut = 14; HalSetLastError(HAL_OK); return HAL_OK;
}


