// TrialManager.secure.cpp — drop-in hardened trial manager
// Features: AES-GCM(AEAD) with AAD bound to softwareId, majority-vote multi-replica storage,
// time rollback freeze, monotonic day accrual, cross-process named mutex, 64-bit time.
// Compile: requires Crypto++ (aes.h, gcm.h, filters.h, osrng.h, sha.h, md5.h)
//
// NOTE:
// - Keep your existing "TrialManager.hpp" API. If signatures differ, see the "ADAPTER" section near the bottom.
// - This file is self-contained aside from Win32 and Crypto++.
//
// Copyright (c) 2025

#include "pch.h"
#include "trialmanager.hpp"
#include "halvault_error.h"
#include "hwid.hpp"
// Optional: if not present, comment out and let fallback RAII below be used.
#include "UniqueHandle.h"

#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0601 // Windows 7
#endif
#include <windows.h>
#include <shlobj.h>     // SHGetKnownFolderPath
#include <wincrypt.h>

#include <cstdint>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <cwchar>
#include <string>
#include <vector>
#include <array>
#include <mutex>
#include <algorithm>
#include <unordered_map>
#include <unordered_map>
#include <unordered_map>

// == Crypto++ ==
#include <cryptopp/aes.h>
#include <cryptopp/gcm.h>
#include <cryptopp/filters.h>
#include <cryptopp/osrng.h>
#include <cryptopp/sha.h>
#include <cryptopp/md5.h>
#include <cryptopp/hmac.h>

#ifndef VMP_GUARD
#define VMP_GUARD(x)
#endif
#ifndef VMP_BLOCK
#define VMP_BLOCK(x)
#endif

// ---- HALVAULT fallback definitions (adjust/remove if your headers provide them) ----
#ifndef HALVAULT_API
#define HALVAULT_API __declspec(dllexport)
#endif

#ifndef HAL_OK
#define HAL_OK 0
#endif
#ifndef HAL_E_INVALIDARG
#define HAL_E_INVALIDARG 0x80070057
#endif
#ifndef HAL_E_TRIAL_EXPIRED
#define HAL_E_TRIAL_EXPIRED 0xA0010001
#endif
#ifndef HAL_E_TRIAL_FROZEN
#define HAL_E_TRIAL_FROZEN  0xA0010002
#endif
#ifndef HAL_E_IO
#define HAL_E_IO            0xA0010003
#endif
#ifndef HAL_E_CRYPTO
#define HAL_E_CRYPTO        0xA0010004
#endif
#ifndef HalSetLastError
static inline void HalSetLastError(int) {}
#endif

// ---- Minimal RAII for HANDLE if UniqueHandle is unavailable ----
#ifndef UNIQUEHANDLE_H
struct HandleCloser {
    void operator()(HANDLE h) const noexcept {
        if (h && h != INVALID_HANDLE_VALUE) ::CloseHandle(h);
    }
};
using UniqueHandle = std::unique_ptr<std::remove_pointer<HANDLE>::type, HandleCloser>;
static UniqueHandle make_unique_handle(HANDLE h) { return UniqueHandle(h); }
#else
// If you already have UniqueHandle.h with make_unique_handle, use it.
#endif

// TrialMode 来自头文件 trial_manager.hpp

// ---- Internal constants ----
static constexpr char   kFileMagic[4] = {'T','R','I','A'};
static constexpr uint8_t kFileVersion = 2;           // encrypted container format version
static constexpr uint32_t kRollbackSlackSec = 300;   // 5min tolerance
static constexpr wchar_t kProductFolder[] = L"HALVault";

// ---- Globals (kept minimal) ----
static std::mutex      g_mutex;         // in-process
static bool            g_inited = false;
static std::string     g_sid;           // softwareId
static std::wstring    g_sidMd5Lower;   // for mutex & folder
static TrialMode       g_mode = TrialMode::Days;
static uint32_t        g_limit = 14;

// ---- Helpers: UTF16/UTF8 ----
/**
 * 功能: 将 UTF-8 字符串转换为 UTF-16 (Windows 宽字符串)。
 * 用法: auto w = Utf8ToWide("abc");
 * 注意: 仅做最小分配; 对于空字符串返回空 wide 字符串。
 */
static std::wstring Utf8ToWide(const std::string& s) {
    if (s.empty()) return std::wstring();
    int n = ::MultiByteToWideChar(CP_UTF8, 0, s.c_str(), (int)s.size(), nullptr, 0);
    std::wstring out; out.resize(n);
    ::MultiByteToWideChar(CP_UTF8, 0, s.c_str(), (int)s.size(), &out[0], n);
    return out;
}

// ---- Helpers: MD5 lower hex for mutex/folder ----
/**
 * 功能: 计算输入字符串的 MD5 并以 32 个小写十六进制宽字符返回。
 * 用法: auto hex = Md5LowerHex(sid);
 * 注意: 用于互斥名/目录名等非安全场景; 安全性由 AES-GCM 负责。
 */
static std::wstring Md5LowerHex(const std::string& s) {
    CryptoPP::Weak1::MD5 md5;
    md5.Update(reinterpret_cast<const CryptoPP::byte*>(s.data()), (size_t)s.size());
    CryptoPP::byte d[CryptoPP::Weak::MD5::DIGESTSIZE]{};
    md5.Final(d);
    static const wchar_t* hex = L"0123456789abcdef";
    std::wstring out; out.resize(32);
    for (int i=0;i<16;++i){ out[i*2+0]=hex[(d[i]>>4)&0xF]; out[i*2+1]=hex[d[i]&0xF]; }
    return out;
}

// ---- Helpers: time ----
/**
 * 功能: 获取当前 UTC 时间(秒, 64 位)。
 * 用法: uint64_t now = UtcNowSec();
 * 注意: 使用 _time64, Win7 可用; 不依赖系统时区。
 */
static inline uint64_t UtcNowSec() noexcept {
    return static_cast<uint64_t>( ::_time64(nullptr) );
}
/**
 * 功能: 将 UTC 秒换算为“自然日索引”(从 Epoch 起按 86400s 划分)。
 * 用法: uint32_t di = DayIndex(UtcNowSec());
 * 注意: 用于按天递增计数, 不受时区切换影响。
 */
static inline uint32_t DayIndex(uint64_t sec) noexcept {
    return static_cast<uint32_t>( sec / 86400ULL );
}

// ---- Key derivation: SHA256(HWID || "HALVault" || softwareId) ----
/**
 * 功能: 派生容器密钥: SHA256(HWID || "HALVault" || softwareId)。
 * 用法: std::array<uint8_t,32> k; DeriveKey(k, sid);
 * 注意: 绑定到设备与产品; 不保存明文密钥; HWID 实现由项目提供。
 */
static void DeriveKey(std::array<uint8_t,32>& outKey, const std::string& sid) {
    VMP_GUARD("Trial_DeriveKey");
    std::string hw = hwid::GetHWIDHex();
    std::string material;
    material.reserve(hw.size()+sid.size()+8);
    material.append(hw);
    material.append("HALVault");
    material.append(sid);
    CryptoPP::SHA256 sha;
    sha.CalculateDigest(outKey.data(),
        reinterpret_cast<const CryptoPP::byte*>(material.data()),
        material.size());
}

// ---- Container AAD binder: magic + version + first 8 bytes of SID MD5 ----
/**
 * 功能: 构造 AEAD 的 AAD(额外认证数据): magic+version+SID-MD5前8字节。
 * 用法: BuildAAD(aad, sid);
 * 注意: 防止跨产品/跨版本重放; 与密钥配合。
 */
static void BuildAAD(std::array<uint8_t,13>& aad, const std::string& sid) {
    aad[0]='T'; aad[1]='R'; aad[2]='I'; aad[3]='A'; aad[4]=kFileVersion;
    // SID MD5 (lower) -> take first 8 bytes of hex as ASCII for AAD (8 chars)
    auto md5hex = Md5LowerHex(sid);
    for (int i=0;i<8;++i) aad[5+i] = static_cast<uint8_t>( md5hex[i] & 0xFF );
}

// ---- Doc v2 (explicit serialization LE) ----
// We keep doc small and explicit to avoid ABI surprises.
struct TrialDocV2 {
    uint8_t  version;        // 2
    uint8_t  mode;           // TrialMode
    uint8_t  frozen;         // 0/1
    uint8_t  reserved0;
    uint32_t limit;          // total runs/days
    uint64_t installUnix;    // first install (UTC)
    uint64_t maxSeenUnix;    // anti-rollback horizon (UTC)
    uint32_t daysAccrued;    // monotonic day counter
    uint32_t lastDayIndex;   // dayIndex of last accrual
    uint32_t runsUsed;       // used runs (for Runs mode)
    uint32_t seqNo;          // monotonic write counter
    uint64_t reserved1;      // future
};
// static size check (not ABI critical due to explicit serialization)
static_assert(sizeof(TrialDocV2)==48, "TrialDocV2 unexpected size");

// LE write/read
static void le32_push(std::vector<uint8_t>& v, uint32_t x){
    v.push_back(uint8_t(x&0xFF)); v.push_back(uint8_t((x>>8)&0xFF));
    v.push_back(uint8_t((x>>16)&0xFF)); v.push_back(uint8_t((x>>24)&0xFF));
}
static void le64_push(std::vector<uint8_t>& v, uint64_t x){
    for(int i=0;i<8;++i) v.push_back(uint8_t((x>>(i*8))&0xFF));
}
static uint32_t le32_read(const uint8_t* p){ return p[0]|(p[1]<<8)|(p[2]<<16)|(p[3]<<24); }
static uint64_t le64_read(const uint8_t* p){
    uint64_t x=0; for(int i=0;i<8;++i) x |= (uint64_t)p[i] << (i*8); return x;
}

/**
 * 功能: 将 TrialDocV2 以小端序显式序列化到字节数组。
 * 用法: Serialize(doc, buf);
 * 注意: 严格 56 字节; 修改结构需同步更新 Deserialize 与 static_assert。
 */
static void Serialize(const TrialDocV2& d, std::vector<uint8_t>& out) {
    out.clear(); out.reserve(64);
    out.push_back(d.version);
    out.push_back(d.mode);
    out.push_back(d.frozen);
    out.push_back(d.reserved0);
    le32_push(out, d.limit);
    le64_push(out, d.installUnix);
    le64_push(out, d.maxSeenUnix);
    le32_push(out, d.daysAccrued);
    le32_push(out, d.lastDayIndex);
    le32_push(out, d.runsUsed);
    le32_push(out, d.seqNo);
    le64_push(out, d.reserved1);
}
/**
 * 功能: 从 56 字节小端序解析 TrialDocV2。
 * 用法: if(Deserialize(buf,d)) ...;
 * 注意: 长度不为 56 直接失败; 版本号需等于 kFileVersion。
 */
static bool Deserialize(const std::vector<uint8_t>& in, TrialDocV2& d) {
    if (in.size() != 48) return false;
    const uint8_t* p = in.data();
    d.version     = p[0];
    d.mode        = p[1];
    d.frozen      = p[2];
    d.reserved0   = p[3];
    d.limit       = le32_read(p+4);
    d.installUnix = le64_read(p+8);
    d.maxSeenUnix = le64_read(p+16);
    d.daysAccrued = le32_read(p+24);
    d.lastDayIndex= le32_read(p+28);
    d.runsUsed    = le32_read(p+32);
    d.seqNo       = le32_read(p+36);
    d.reserved1   = le64_read(p+40);
    return d.version==kFileVersion;
}

// ---- AES-GCM encrypt/decrypt container ----
/**
 * 功能: 使用 AES-GCM 加密 TrialDocV2, 输出: magic|version|IV|cipher+tag。
 * 用法: EncryptDoc(doc, bytes, sid);
 * 注意: 随机 96-bit IV; AAD 绑定产品; 失败返回 false 不抛异常。
 */
static bool EncryptDoc(const TrialDocV2& doc, std::vector<uint8_t>& out, const std::string& sid) {
    VMP_BLOCK("Trial_EncryptDoc");
    std::array<uint8_t,32> key{}; DeriveKey(key, sid);
    CryptoPP::AutoSeededRandomPool prng;
    std::array<uint8_t,12> iv{}; prng.GenerateBlock(iv.data(), iv.size());
    std::array<uint8_t,13> aad{}; BuildAAD(aad, sid);

    std::vector<uint8_t> plain; Serialize(doc, plain);

    CryptoPP::GCM<CryptoPP::AES>::Encryption enc;
    enc.SetKeyWithIV(key.data(), key.size(), iv.data(), iv.size());

    std::string cipher;
    try {
        CryptoPP::AuthenticatedEncryptionFilter aef(
            enc, new CryptoPP::StringSink(cipher));
        aef.ChannelPut(CryptoPP::AAD_CHANNEL, aad.data(), aad.size());
        aef.ChannelMessageEnd(CryptoPP::AAD_CHANNEL);
        aef.Put(plain.data(), plain.size());
        aef.MessageEnd();
    } catch (...) { return false; }

    out.clear(); out.reserve(4+1+12+cipher.size());
    out.insert(out.end(), kFileMagic, kFileMagic+4);
    out.push_back(kFileVersion);
    out.insert(out.end(), iv.begin(), iv.end());
    out.insert(out.end(), cipher.begin(), cipher.end());
    return true;
}

/**
 * 功能: 解密并验证容器; 通过后反序列化为 TrialDocV2。
 * 用法: if (DecryptDoc(bytes, doc, sid)) ...
 * 注意: 检查 magic/version; AAD+Tag 校验失败直接返回 false。
 */
static bool DecryptDoc(const std::vector<uint8_t>& in, TrialDocV2& outDoc, const std::string& sid) {
    if (in.size() < 4+1+12+16) return false;
    if (std::memcmp(in.data(), kFileMagic, 4)!=0) return false;
    if (in[4]!=kFileVersion) return false;
    const uint8_t* iv = in.data()+5;
    const uint8_t* c  = in.data()+5+12;
    size_t clen = in.size() - (5+12);

    std::array<uint8_t,32> key{}; DeriveKey(key, sid);
    std::array<uint8_t,13> aad{}; BuildAAD(aad, sid);

    CryptoPP::GCM<CryptoPP::AES>::Decryption dec;
    dec.SetKeyWithIV(key.data(), key.size(), iv, 12);

    std::string plain;
    try {
        CryptoPP::AuthenticatedDecryptionFilter adf(
            dec, new CryptoPP::StringSink(plain),
            CryptoPP::AuthenticatedDecryptionFilter::THROW_EXCEPTION);
        adf.ChannelPut(CryptoPP::AAD_CHANNEL, aad.data(), aad.size());
        adf.ChannelMessageEnd(CryptoPP::AAD_CHANNEL);
        adf.Put(c, clen);
        adf.MessageEnd();
    } catch (...) { return false; }

    std::vector<uint8_t> buf(plain.begin(), plain.end());
    return Deserialize(buf, outDoc);
}

// ---- Storage targets ----
struct Target { std::wstring path; std::wstring ads; };

/**
 * 功能: 读取已知文件夹路径 (如 LocalAppData/Roaming)。
 * 用法: auto p = GetKnownFolder(FOLDERID_LocalAppData);
 * 注意: Win7 可用; 返回空表示失败。
 */
static std::wstring GetKnownFolder(REFKNOWNFOLDERID id) {
    PWSTR p = nullptr; std::wstring out;
    if (SUCCEEDED(::SHGetKnownFolderPath(id, KF_FLAG_DEFAULT, nullptr, &p))) {
        out.assign(p); ::CoTaskMemFree(p);
    }
    return out;
}
/**
 * 功能: 以反斜杠合并两个 Windows 路径片段。
 * 用法: auto p = Join(base, L"trial.bin");
 * 注意: 自动处理尾部分隔符; 避免双斜杠。
 */
static std::wstring Join(const std::wstring& a, const std::wstring& b){
    if (a.empty()) return b; if (b.empty()) return a;
    wchar_t sep = L'\\';
    if (a.back()==sep) return a+b;
    return a + sep + b;
}
/**
 * 功能: 确保目录存在; 不存在则创建。
 * 用法: EnsureDir(path);
 * 注意: 使用 SHCreateDirectoryExW; 对 FAT/网络盘同样有效。
 */
static bool EnsureDir(const std::wstring& dir) {
    DWORD attr = ::GetFileAttributesW(dir.c_str());
    if (attr!=INVALID_FILE_ATTRIBUTES && (attr & FILE_ATTRIBUTE_DIRECTORY)) return true;
    return ::SHCreateDirectoryExW(nullptr, dir.c_str(), nullptr) == ERROR_SUCCESS;
}

// Local evidence log path in LocalAppData
/**
 * 功能: 获取当前用户 LocalAppData 下的证据日志路径。
 * 用法: auto lp = LocalLogPath(md5);
 * 注意: 会确保父目录存在; 日志是追加写。
 */
static std::wstring LocalLogPath(const std::wstring& sidMd5Lower){
    std::wstring base = Join(GetKnownFolder(FOLDERID_LocalAppData),  Join(kProductFolder, sidMd5Lower));
    EnsureDir(base);
    return Join(base, L"trial.log");
}

/**
 * 功能: 构建多副本落盘目标(主文件+ADS, Local 与 Roaming)。
 * 用法: auto t = BuildTargets(md5);
 * 注意: 若文件系统不支持 ADS, 写入会失败但不致命; 多数投票仍成立。
 */
static std::vector<Target> BuildTargets(const std::wstring& sidMd5Lower) {
    std::vector<Target> t;
    std::wstring base1 = Join(GetKnownFolder(FOLDERID_LocalAppData),  Join(kProductFolder, sidMd5Lower));
    std::wstring base2 = Join(GetKnownFolder(FOLDERID_RoamingAppData), Join(kProductFolder, sidMd5Lower));
    EnsureDir(base1); EnsureDir(base2);

    // Primary files
    t.push_back({ Join(base1, L"trial.bin"), L"" });
    t.push_back({ Join(base1, L"trial.bin"), L":alt" }); // ADS on same file
    t.push_back({ Join(base2, L"trial.bin"), L"" });
    t.push_back({ Join(base2, L"trial.bin"), L":alt" });
    return t;
}

// ---- Registry replicas (HKCU) ----
/**
 * 功能: 写入 HKCU 注册表二进制值(副本之一)。
 * 用法: RegWriteBlob(md5, L"trial", bytes);
 * 注意: 路径为 HKCU\Software\HALVault\{md5}; 标准用户可写。
 */
/**
 * 功能: 写入 HKCU 注册表二进制值(副本之一)。
 * 用法: RegWriteBlob(md5, L"trial", bytes);
 * 注意: 路径为 HKCU\\Software\\HALVault\\{md5}; 标准用户可写。
 */
/**
 * 功能: 写入 HKCU 注册表二进制值(副本之一)。
 * 用法: RegWriteBlob(md5, L"trial", bytes);
 * 注意: 路径为 HKCU\\Software\\HALVault\\{md5}; 标准用户可写。
 */
/**
 * 功能: 写入 HKCU 注册表二进制值(副本之一)。
 * 用法: RegWriteBlob(md5, L"trial", bytes);
 * 注意: 路径为 HKCU\\Software\\HALVault\\{md5}; 标准用户可写。
 */
/**
 * 功能: 写入 HKCU 注册表二进制值(副本之一)。
 * 用法: RegWriteBlob(md5, L"trial", bytes);
 * 注意: 路径为 HKCU\\Software\\HALVault\\{md5}; 标准用户可写。
 */
/**
 * 功能: 写入 HKCU 注册表二进制值(副本之一)。
 * 用法: RegWriteBlob(md5, L"trial", bytes);
 * 注意: 路径为 HKCU\Software\HALVault\{md5}; 标准用户可写。
 */
static bool RegWriteBlob(const std::wstring& sidMd5Lower, const wchar_t* valueName, const std::vector<uint8_t>& blob) {
    HKEY hKey = nullptr;
    std::wstring subkey = L"Software\\HALVault\\" + sidMd5Lower;
    LSTATUS s = ::RegCreateKeyExW(HKEY_CURRENT_USER, subkey.c_str(), 0, nullptr, 0, KEY_SET_VALUE, nullptr, &hKey, nullptr);
    if (s != ERROR_SUCCESS) return false;
    s = ::RegSetValueExW(hKey, valueName, 0, REG_BINARY,
                         reinterpret_cast<const BYTE*>(blob.data()),
                         (DWORD)blob.size());
    ::RegCloseKey(hKey);
    return s == ERROR_SUCCESS;
}

static bool RegReadBlob(const std::wstring& sidMd5Lower, const wchar_t* valueName, std::vector<uint8_t>& out) {
    HKEY hKey = nullptr;
    std::wstring subkey = L"Software\\HALVault\\" + sidMd5Lower;
    LSTATUS s = ::RegOpenKeyExW(HKEY_CURRENT_USER, subkey.c_str(), 0, KEY_QUERY_VALUE, &hKey);
    if (s != ERROR_SUCCESS) return false;
    DWORD type=0, size=0;
    s = ::RegQueryValueExW(hKey, valueName, nullptr, &type, nullptr, &size);
    if (s != ERROR_SUCCESS || type != REG_BINARY || size==0 || size > (10*1024*1024)) { ::RegCloseKey(hKey); return false; }
    out.resize(size);
    s = ::RegQueryValueExW(hKey, valueName, nullptr, &type, out.data(), &size);
    ::RegCloseKey(hKey);
    return s == ERROR_SUCCESS;
}




/**
 * 功能: RegWriteBlob 的薄包装, 方便未来切换实现。
 * 用法: RegWriteBlob2(md5, L"alt", bytes);
 * 注意: 与 RegWriteBlob 行为等价。
 */
static bool RegWriteBlob2(const std::wstring& sidMd5Lower, const wchar_t* valueName, const std::vector<uint8_t>& blob) {
    return RegWriteBlob(sidMd5Lower, valueName, blob);
}
/**
 * 功能: RegReadBlob 的薄包装。
 * 用法: RegReadBlob2(md5, L"alt", out);
 * 注意: 与 RegReadBlob 行为等价。
 */
static bool RegReadBlob2(const std::wstring& sidMd5Lower, const wchar_t* valueName, std::vector<uint8_t>& out) {
    return RegReadBlob(sidMd5Lower, valueName, out);
}
/**
 * 功能: 同时写入 trial/alt 两个注册表值; 任一成功视为成功。
 * 用法: RegWriteBoth(md5, bytes);
 * 注意: 提升鲁棒性, 与文件副本共同组成多数投票。
 */
static bool RegWriteBoth(const std::wstring& sidMd5Lower, const std::vector<uint8_t>& blob) {
    bool a = RegWriteBlob2(sidMd5Lower, L"trial", blob);
    bool b = RegWriteBlob2(sidMd5Lower, L"alt", blob);
    return a || b;
}

// ---- Authenticated & trimmed evidence log (LocalAppData) ----
#pragma pack(push,1)
struct LogRecV1 { uint32_t tag; uint32_t seqNo; uint64_t maxSeen; uint32_t daysAccrued; };
struct LogRecV2 { uint32_t tag; uint32_t seqNo; uint64_t maxSeen; uint32_t daysAccrued; uint8_t mac[16]; };
#pragma pack(pop)
static constexpr uint32_t kLogTagV1 = ('T') | ('R'<<8) | ('L'<<16) | ('G'<<24); // 'TRLG'
static constexpr uint32_t kLogTagV2 = ('T') | ('L'<<8) | ('G'<<16) | ('2'<<24); // 'TLG2'
static constexpr size_t kLogMaxRecords = 4096;
static constexpr size_t kLogTrimKeep  = 1024;

/**
 * 功能: 推导日志 HMAC 密钥(基于容器密钥再加盐)。
 * 用法: DeriveLogKey(k, sid);
 * 注意: 与容器密钥域隔离, 防止密钥复用风险。
 */
static void DeriveLogKey(std::array<uint8_t,32>& key, const std::string& sid) {
    std::array<uint8_t,32> base{}; DeriveKey(base, sid);
    CryptoPP::SHA256 sha;
    const char salt[] = "HALVault-LOGv1";
    std::string material(reinterpret_cast<const char*>(base.data()), base.size());
    material.append(salt, sizeof(salt)-1);
    sha.CalculateDigest(key.data(), reinterpret_cast<const CryptoPP::byte*>(material.data()), material.size());
}

/**
 * 功能: 当日志记录数超过阈值时裁剪，仅保留尾部最近 kLogTrimKeep 条。
 * 用法: LogTrimIfNeeded(md5);
 * 注意: 自动识别 V1/V2 记录大小; 支持就地裁剪。
 */
static void LogTrimIfNeeded(const std::wstring& sidMd5Lower){
    std::wstring p = LocalLogPath(sidMd5Lower);
    HANDLE hr = ::CreateFileW(p.c_str(), GENERIC_READ|GENERIC_WRITE, FILE_SHARE_READ, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (hr==INVALID_HANDLE_VALUE) return;
    LARGE_INTEGER li{}; if (!::GetFileSizeEx(hr, &li)) { ::CloseHandle(hr); return; }
    ULONGLONG sz = (ULONGLONG)li.QuadPart;
    if (sz==0) { ::CloseHandle(hr); return; }
    auto trim_tail = [&](size_t recSize, size_t n){
        if (n <= kLogMaxRecords) return;
        size_t keep = (std::min)(kLogTrimKeep, n);
        std::vector<uint8_t> buf(keep * recSize);
        LARGE_INTEGER off{}; off.QuadPart = (LONGLONG)((n - keep) * recSize);
        ::SetFilePointerEx(hr, off, nullptr, FILE_BEGIN);
        DWORD rd=0; ::ReadFile(hr, buf.data(), (DWORD)buf.size(), &rd, nullptr);
        ::SetFilePointer(hr, 0, nullptr, FILE_BEGIN);
        ::SetEndOfFile(hr);
        DWORD wr=0; ::WriteFile(hr, buf.data(), (DWORD)buf.size(), &wr, nullptr);
    };
    if (sz % sizeof(LogRecV2) == 0) {
        size_t n = (size_t)(sz / sizeof(LogRecV2));
        trim_tail(sizeof(LogRecV2), n);
    } else if (sz % sizeof(LogRecV1) == 0) {
        size_t n = (size_t)(sz / sizeof(LogRecV1));
        trim_tail(sizeof(LogRecV1), n);
    }
    ::CloseHandle(hr);
}

/**
 * 功能: 以 V2 记录格式追加写入日志, 并附带 HMAC 校验码。
 * 用法: LogAppend2(md5, doc, sid);
 * 注意: 失败静默, 不影响主流程; 末尾会触发裁剪。
 */
static void LogAppend2(const std::wstring& sidMd5Lower, const TrialDocV2& d, const std::string& sid){
    std::wstring p = LocalLogPath(sidMd5Lower);
    HANDLE h = ::CreateFileW(p.c_str(), FILE_APPEND_DATA, FILE_SHARE_READ, nullptr,
                             OPEN_ALWAYS, FILE_ATTRIBUTE_HIDDEN|FILE_ATTRIBUTE_NOT_CONTENT_INDEXED, nullptr);
    if (h==INVALID_HANDLE_VALUE) return;
    LogRecV2 rec{};
    rec.tag = kLogTagV2; rec.seqNo = d.seqNo; rec.maxSeen = d.maxSeenUnix; rec.daysAccrued = d.daysAccrued;
    std::array<uint8_t,32> key{}; DeriveLogKey(key, sid);
    CryptoPP::HMAC<CryptoPP::SHA256> hmac(key.data(), key.size());
    hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&rec.tag), sizeof(rec.tag));
    hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&rec.seqNo), sizeof(rec.seqNo));
    hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&rec.maxSeen), sizeof(rec.maxSeen));
    hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&rec.daysAccrued), sizeof(rec.daysAccrued));
    auto md5hex = Md5LowerHex(sid);
    hmac.Update(reinterpret_cast<const CryptoPP::byte*>(md5hex.data()), 8*sizeof(wchar_t));
    std::array<uint8_t,32> full{}; hmac.Final(full.data());
    std::memcpy(rec.mac, full.data(), 16);
    DWORD wr=0; ::WriteFile(h, &rec, sizeof(rec), &wr, nullptr);
    ::CloseHandle(h);
    LogTrimIfNeeded(sidMd5Lower);
}

/**
 * 功能: 读取并聚合日志证据(最大 seq/maxSeen/days), 指示是否通过 HMAC 验证。
 * 用法: LogReadEvidence2(md5, sid, seq, maxSeen, days, authed);
 * 注意: 若全为旧 V1 记录, hasAuth=false, 将采用更宽松的触发阈值。
 */
static void LogReadEvidence2(const std::wstring& sidMd5Lower, const std::string& sid,
                             uint32_t& seqMax, uint64_t& maxSeenMax, uint32_t& daysMax, bool& hasAuth){
    seqMax=0; maxSeenMax=0; daysMax=0; hasAuth=false;
    std::wstring p = LocalLogPath(sidMd5Lower);
    HANDLE h = ::CreateFileW(p.c_str(), GENERIC_READ, FILE_SHARE_READ|FILE_SHARE_WRITE, nullptr,
                             OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (h==INVALID_HANDLE_VALUE) return;
    LARGE_INTEGER li{}; if (!::GetFileSizeEx(h, &li)) { ::CloseHandle(h); return; }
    ULONGLONG sz = (ULONGLONG)li.QuadPart;
    if (sz==0) { ::CloseHandle(h); return; }

    std::array<uint8_t,32> key{}; DeriveLogKey(key, sid);
    CryptoPP::HMAC<CryptoPP::SHA256> hmac(key.data(), key.size());

    if (sz % sizeof(LogRecV2) == 0) {
        size_t n = (size_t)(sz / sizeof(LogRecV2));
        std::vector<LogRecV2> buf(n);
        DWORD rd=0; BOOL ok = ::ReadFile(h, buf.data(), (DWORD)(n*sizeof(LogRecV2)), &rd, nullptr);
        ::CloseHandle(h);
        if (!ok) return;
        for (const auto& r : buf){
            if (r.tag != kLogTagV2) continue;
            std::array<uint8_t,32> mac{}; hmac.Restart();
            hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&r.tag), sizeof(r.tag));
            hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&r.seqNo), sizeof(r.seqNo));
            hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&r.maxSeen), sizeof(r.maxSeen));
            hmac.Update(reinterpret_cast<const CryptoPP::byte*>(&r.daysAccrued), sizeof(r.daysAccrued));
            auto md5hex = Md5LowerHex(sid);
            hmac.Update(reinterpret_cast<const CryptoPP::byte*>(md5hex.data()), 8*sizeof(wchar_t));
            hmac.Final(mac.data());
            if (std::memcmp(mac.data(), r.mac, 16)!=0) continue;
            hasAuth = true;
            seqMax = (std::max)(seqMax, r.seqNo);
            maxSeenMax = (std::max)<uint64_t>(maxSeenMax, r.maxSeen);
            daysMax = (std::max)(daysMax, r.daysAccrued);
        }
        return;
    }

    if (sz % sizeof(LogRecV1) == 0) {
        size_t n = (size_t)(sz / sizeof(LogRecV1));
        std::vector<LogRecV1> buf(n);
        DWORD rd=0; BOOL ok = ::ReadFile(h, buf.data(), (DWORD)(n*sizeof(LogRecV1)), &rd, nullptr);
        ::CloseHandle(h);
        if (!ok) return;
        for (const auto& r : buf){
            if (r.tag != kLogTagV1) continue;
            seqMax = std::max(seqMax, r.seqNo);
            maxSeenMax = std::max<uint64_t>(maxSeenMax, r.maxSeen);
            daysMax = std::max(daysMax, r.daysAccrued);
        }
        return;
    }
    ::CloseHandle(h);
}

/**
 * 功能: 将同一密文写入所有文件副本目标(含 ADS)。
 * 用法: WriteAllTargets(bytes, targets);
 * 注意: 逐个 best-effort; 任一成功即可。
 */
static bool WriteAllTargets(const std::vector<uint8_t>& blob, const std::vector<Target>& t) {
    bool any = false;
    for (const auto& x : t) {
        std::wstring p = x.path + x.ads;
        HANDLE h = ::CreateFileW(p.c_str(), GENERIC_WRITE, FILE_SHARE_READ, nullptr,
                                 CREATE_ALWAYS, FILE_ATTRIBUTE_HIDDEN|FILE_ATTRIBUTE_NOT_CONTENT_INDEXED, nullptr);
        if (h==INVALID_HANDLE_VALUE) continue;
        DWORD wr=0; BOOL ok = ::WriteFile(h, blob.data(), (DWORD)blob.size(), &wr, nullptr);
        ::FlushFileBuffers(h);
        ::CloseHandle(h);
        if (ok && wr == (DWORD)blob.size()) any = true;
    }
    return any;
}

/**
 * 功能: 读取单一目标(主文件或 ADS)到内存。
 * 用法: ReadOne(t, buf);
 * 注意: 限制最大 10MB; 失败返回 false。
 */
static bool ReadOne(const Target& t, std::vector<uint8_t>& out) {
    std::wstring p = t.path + t.ads;
    HANDLE h = ::CreateFileW(p.c_str(), GENERIC_READ, FILE_SHARE_READ|FILE_SHARE_WRITE, nullptr,
                             OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (h==INVALID_HANDLE_VALUE) return false;
    DWORD sz = ::GetFileSize(h, nullptr);
    if (sz==INVALID_FILE_SIZE || sz==0 || sz> (10*1024*1024)) { ::CloseHandle(h); return false; }
    out.resize(sz);
    DWORD rd=0; BOOL ok = ::ReadFile(h, out.data(), sz, &rd, nullptr);
    ::CloseHandle(h);
    return ok && rd==sz;
}

/**
 * 功能: 多数投票选择最可信的文档; 如无多数则选择 seqNo 最大者。
 * 用法: size_t idx; MajoritySelect(list, idx);
 * 注意: 简化多数判定(字段一致即视为同票)。
 */
/**
 * 功能: 多数投票选择最可信的文档; 如无多数则选择 seqNo 最大者。
 * 用法: size_t idx; MajoritySelect(list, idx);
 * 注意: 使用哈希聚合 O(n)；字段一致即视为同票。
 */
/**
 * 功能: 多数投票选择最可信的文档; 如无多数则选择 seqNo 最大者。
 * 用法: size_t idx; MajoritySelect(list, idx);
 * 注意: 使用哈希聚合 O(n)；字段一致即视为同票。
 */
static bool MajoritySelect(const std::vector<TrialDocV2>& docs, size_t& pick) {
    if (docs.empty()) return false;
    struct Key {
        uint64_t maxSeenUnix; uint32_t daysAccrued; uint32_t runsUsed; uint64_t installUnix; uint32_t seqNo;
        bool operator==(const Key& o) const noexcept {
            return maxSeenUnix==o.maxSeenUnix && daysAccrued==o.daysAccrued &&
                   runsUsed==o.runsUsed && installUnix==o.installUnix && seqNo==o.seqNo;
        }
    };
    struct H {
        size_t operator()(const Key& k) const noexcept {
            auto h1 = std::hash<uint64_t>{}(k.maxSeenUnix);
            auto h2 = std::hash<uint32_t>{}(k.daysAccrued);
            auto h3 = std::hash<uint32_t>{}(k.runsUsed);
            auto h4 = std::hash<uint64_t>{}(k.installUnix);
            auto h5 = std::hash<uint32_t>{}(k.seqNo);
            return (((h1 ^ (h2<<1)) ^ (h3<<1)) ^ (h4<<1)) ^ (h5<<1);
        }
    };
    std::unordered_map<Key, std::pair<int,size_t>, H> cnt;
    for (size_t i=0;i<docs.size();++i){
        const auto& d = docs[i]; Key k{d.maxSeenUnix,d.daysAccrued,d.runsUsed,d.installUnix,d.seqNo};
        auto& e = cnt[k]; if (e.first==0) e.second=i; e.first++;
    }
    int bestC=0; size_t bestIdx=0; uint32_t bestSeq=0;
    for (auto& kv : cnt){
        int c = kv.second.first; size_t idx = kv.second.second; uint32_t seq = docs[idx].seqNo;
        if (c>bestC || (c==bestC && seq>=bestSeq)) { bestC=c; bestIdx=idx; bestSeq=seq; }
    }
    pick = bestIdx; return true;
}



/**
 * 功能: 从文件+注册表副本加载所有可解密文档, 通过多数投票挑选最佳。
 * 用法: TrialDocV2 d; bool ok = LoadBestDoc(sid, md5, d);
 * 注意: 失败返回 false; 不创建新文档。
 */
static bool LoadBestDoc(const std::string& sid, const std::wstring& sidMd5Lower, TrialDocV2& out) {
    auto targets = BuildTargets(sidMd5Lower);
    std::vector<TrialDocV2> valid;
    for (auto& t : targets) {
        std::vector<uint8_t> blob;
        if (!ReadOne(t, blob)) continue;
        TrialDocV2 d{};
        if (!DecryptDoc(blob, d, sid)) continue;
        if (d.version!=kFileVersion) continue;
        valid.push_back(d);
    }
    // Registry replicas
    {
        std::vector<uint8_t> rblob; TrialDocV2 rd{};
        if (RegReadBlob2(sidMd5Lower, L"trial", rblob)) {
            if (DecryptDoc(rblob, rd, sid) && rd.version==kFileVersion) valid.push_back(rd);
        }
        rblob.clear();
        if (RegReadBlob2(sidMd5Lower, L"alt", rblob)) {
            if (DecryptDoc(rblob, rd, sid) && rd.version==kFileVersion) valid.push_back(rd);
        }
    }
    if (valid.empty()) return false;
    size_t idx=0; MajoritySelect(valid, idx);
    out = valid[idx];
    return true;
}

/**
 * 功能: 将文档加密并写入所有副本(文件+注册表), 成功则追加日志。
 * 用法: SaveDocAll(sid, md5, doc);
 * 注意: 至少有一处成功才返回 true; 失败不写日志。
 */
static bool SaveDocAll(const std::string& sid, const std::wstring& sidMd5Lower, const TrialDocV2& doc) {
    std::vector<uint8_t> blob;
    if (!EncryptDoc(doc, blob, sid)) return false;
    auto targets = BuildTargets(sidMd5Lower);
    bool okFile = WriteAllTargets(blob, targets);
    bool okReg  = RegWriteBoth(sidMd5Lower, blob);
    if (okFile || okReg) LogAppend2(sidMd5Lower, doc, sid);
    return okFile || okReg;
}

// ---- Named mutex for cross-process ----
/**
 * 功能: 跨进程命名互斥(会话级 Local\HALVault_{md5})，防止并发写。
 * 用法: auto m = AcquireNamedMutex(md5);
 * 注意: 等待 8s; 获取失败时仍继续(降级, 但可能并发)。
 */
/**
 * 功能: 跨进程命名互斥(会话级 Local\HALVault_{md5})，防止并发写。
 * 用法: auto m = AcquireNamedMutex(md5);
 * 注意: 等待 8s; 获取失败时仍继续(降级, 但可能并发)。
 */
static UniqueHandle AcquireNamedMutex(const std::wstring& sidMd5Lower) {
    std::wstring name = L"Local\\HALVault_" + sidMd5Lower;
    HANDLE h = ::CreateMutexW(nullptr, FALSE, name.c_str());
    if (!h) return UniqueHandle{};
    DWORD wait = ::WaitForSingleObject(h, 8000);
    if (wait==WAIT_OBJECT_0 || wait==WAIT_ABANDONED) { return make_unique_handle(h); }
    ::CloseHandle(h); return UniqueHandle{};
}


// ---- Core evaluation/update ----
static constexpr uint8_t kFlagMixed = 0x01; // reserved0 bit0

/** 解包“次数→天数”混合策略参数 */
static inline void UnpackMixed(uint64_t packed, uint32_t& runsFirst, uint32_t& daysAfter){
    runsFirst = (uint32_t)(packed >> 32);
    daysAfter = (uint32_t)(packed & 0xFFFFFFFFu);
}
/** 打包“次数→天数”混合策略参数 */
static inline uint64_t PackMixed(uint32_t runsFirst, uint32_t daysAfter){
    return (uint64_t(runsFirst) << 32) | uint64_t(daysAfter);
}
/** 安全累加天数（饱和加法），并在跨日时推进 lastDayIndex */
static inline void SafeAccrueDays(TrialDocV2& d, uint32_t today){
    if (today > d.lastDayIndex) {
        uint32_t delta = today - d.lastDayIndex;
        uint32_t acc = d.daysAccrued;
        d.daysAccrued = (UINT32_MAX - acc < delta) ? UINT32_MAX : (acc + delta);
        d.lastDayIndex = today;
    }
}

/** 装载或初始化文档（缺失时初始化并落盘） */
static int LoadOrInitDoc(TrialDocV2& d, uint64_t now, uint32_t today){
    bool has = LoadBestDoc(g_sid, g_sidMd5Lower, d);
    if (has) return HAL_OK;
    d.version = kFileVersion; d.mode=(uint8_t)g_mode; d.frozen=0; d.reserved0=0; d.limit=g_limit;
    d.installUnix=now; d.maxSeenUnix=now; d.daysAccrued=0; d.lastDayIndex=today;
    d.runsUsed=0; d.seqNo=1; d.reserved1=0;
    if (!SaveDocAll(g_sid, g_sidMd5Lower, d)) { HalSetLastError(HAL_E_IO); return HAL_E_IO; }
    return HAL_OK;
}

/** 侧信道证据判定（带 HMAC 的日志优先，可能直接冻结并返回） */
static int EvidenceGuard(TrialDocV2& d, uint64_t /*now*/, uint32_t* remainingOut, uint32_t* totalOut){
    uint32_t evSeq=0, evDays=0; uint64_t evMax=0; bool evAuth=false;
    LogReadEvidence2(g_sidMd5Lower, g_sid, evSeq, evMax, evDays, evAuth);
    bool strongTrigger = (evMax > d.maxSeenUnix + kRollbackSlackSec) || (evSeq > d.seqNo + 3);
    bool weakTrigger   = (evMax > d.maxSeenUnix + kRollbackSlackSec*6) || (evSeq > d.seqNo + 8);
    if ( (evAuth && strongTrigger) || (!evAuth && weakTrigger) ) {
        d.frozen = 1;
        SaveDocAll(g_sid, g_sidMd5Lower, d);
        if (remainingOut) *remainingOut = 0; if (totalOut) *totalOut = d.limit;
        HalSetLastError(HAL_E_TRIAL_FROZEN);
        return HAL_E_TRIAL_FROZEN;
    }
    return HAL_OK;
}

/** 仅基于“系统时间回拨”的温和冻结路径（无日志强证据时提高阈值） */
static int ClockOnlyRollbackCheck(TrialDocV2& d, uint64_t now, uint32_t* remainingOut, uint32_t* totalOut){
    if (d.maxSeenUnix > now) {
        uint64_t diff = d.maxSeenUnix - now;
        if (diff > (kRollbackSlackSec * 3ULL)) {
            d.frozen = 1; SaveDocAll(g_sid, g_sidMd5Lower, d);
            if (remainingOut) *remainingOut = 0; if (totalOut) *totalOut = d.limit;
            HalSetLastError(HAL_E_TRIAL_FROZEN); return HAL_E_TRIAL_FROZEN;
        }
    }
    return HAL_OK;
}

/** 推进时间地平线并在时间恢复时自动解冻 */
static inline void AdvanceHorizonAndThaw(TrialDocV2& d, uint64_t now){
    if (now > d.maxSeenUnix) d.maxSeenUnix = now;
    if (d.frozen && now >= d.maxSeenUnix) d.frozen = 0;
}

/** 计算额度并在需要时消耗一次 */
static void ComputeQuotaAndMaybeConsume(TrialDocV2& d, uint32_t today, bool consumeRun, uint32_t& remaining, uint32_t& totalLimit){
    bool mixed = (d.reserved0 & kFlagMixed) != 0;
    uint32_t runsFirst=0, daysAfter=0; if (mixed) UnpackMixed(d.reserved1, runsFirst, daysAfter);

    if (mixed) {
        if (d.runsUsed < runsFirst) {
            if (consumeRun && d.runsUsed < runsFirst) d.runsUsed += 1;
            remaining = (runsFirst > d.runsUsed) ? (runsFirst - d.runsUsed) : 0;
            totalLimit = runsFirst;
        } else {
            SafeAccrueDays(d, today);
            remaining = (daysAfter > d.daysAccrued) ? (daysAfter - d.daysAccrued) : 0;
            totalLimit = daysAfter;
        }
        return;
    }

    if ((TrialMode)d.mode == TrialMode::Days) {
        SafeAccrueDays(d, today);
        remaining = (d.limit > d.daysAccrued) ? (d.limit - d.daysAccrued) : 0;
        totalLimit = d.limit;
    } else {
        if (consumeRun && d.runsUsed < d.limit) d.runsUsed += 1;
        remaining = (d.limit > d.runsUsed) ? (d.limit - d.runsUsed) : 0;
        totalLimit = d.limit;
    }
}

/** 主流程（薄壳）：获取互斥 → 装载/初始化 → 证据检查 → 时钟检查 → 推进/解冻 → 计算额度 → 持久化 */
static int CoreEvaluate(bool consumeRun, uint32_t* remainingOut, uint32_t* totalOut) {
    if (!g_inited) { HalSetLastError(HAL_E_INVALIDARG); return HAL_E_INVALIDARG; }

    auto mtx = AcquireNamedMutex(g_sidMd5Lower); // 互斥获取失败也继续，最坏并发有多数投票自愈

    TrialDocV2 d{};
    uint64_t now = UtcNowSec();
    uint32_t today = DayIndex(now);

    {
        int rc = LoadOrInitDoc(d, now, today);
        if (rc != HAL_OK) return rc;
    }

    {
        int rc = EvidenceGuard(d, now, remainingOut, totalOut);
        if (rc != HAL_OK) return rc; // 已冻结并保存
    }

    {
        int rc = ClockOnlyRollbackCheck(d, now, remainingOut, totalOut);
        if (rc != HAL_OK) return rc; // 已冻结并保存
    }

    AdvanceHorizonAndThaw(d, now);

    uint32_t remaining=0, totalLimit=0;
    ComputeQuotaAndMaybeConsume(d, today, consumeRun, remaining, totalLimit);

    d.seqNo += 1;
    if (!SaveDocAll(g_sid, g_sidMd5Lower, d)) { HalSetLastError(HAL_E_IO); return HAL_E_IO; }

    if (remainingOut) *remainingOut = remaining;
    if (totalOut) *totalOut = totalLimit;

    if (d.frozen) { HalSetLastError(HAL_E_TRIAL_FROZEN); return HAL_E_TRIAL_FROZEN; }
    if (remaining==0) { HalSetLastError(HAL_E_TRIAL_EXPIRED); return HAL_E_TRIAL_EXPIRED; }
    HalSetLastError(HAL_OK); return HAL_OK;
}

// =====================================================================================
//                                      API
// =====================================================================================

/**
 * 功能: 初始化试用系统(设置软件ID/模式/额度)。
 * 用法: TrialInitialize("VE-123", TrialMode::Days, 14);
 * 注意: 线程安全; 会触发一次 CoreEvaluate 以确保落盘存在。
 */
extern "C" HALVAULT_API int TrialInitialize(const char* softwareId,
                                            TrialMode mode, uint32_t limit) noexcept
{
    VMP_GUARD("TrialInitialize");
    if (!softwareId) { HalSetLastError(HAL_E_INVALIDARG); return HAL_E_INVALIDARG; }
    std::lock_guard<std::mutex> lk(g_mutex);
    g_sid.assign(softwareId);
    g_mode  = mode;
    g_limit = (limit==0? 1 : limit);
    g_sidMd5Lower = Md5LowerHex(g_sid);
    g_inited = true;

    // Force-create doc if none
    uint32_t rem=0; return CoreEvaluate(false, &rem, nullptr);
}

/**
 * 功能: 查询当前剩余额度与总额度(不消耗)。
 * 用法: TrialEvaluate(&remain, &total);
 * 注意: 若被冻结/过期, 通过返回值与 HalSetLastError 体现。
 */
extern "C" HALVAULT_API int TrialEvaluate(uint32_t* remainingOut,
                                          uint32_t* totalLimitOut) noexcept
{
    VMP_GUARD("TrialEvaluate");
    return CoreEvaluate(false, remainingOut, totalLimitOut);
}

/**
 * 功能: 在“按次数”阶段/模式下消耗一次额度。
 * 用法: TrialConsumeOneRun(&remain, &total);
 * 注意: 在“按天”模式下不会增加 days; 混合策略阶段1消耗次数。
 */
extern "C" HALVAULT_API int TrialConsumeOneRun(uint32_t* remainingOut,
                                               uint32_t* totalLimitOut) noexcept
{
    VMP_GUARD("TrialConsumeOneRun");
    return CoreEvaluate(true, remainingOut, totalLimitOut);
}

// Default policy by softwareId prefix (adjust rules to your fleet)
/**
 * 功能: 根据软件ID前缀给出默认试用策略。
 * 用法: TrialGetDefaultConfig(sid, mode, limit);
 * 注意: 可按产品线自定义前缀规则; 未匹配走默认 14 天。
 */
extern "C" HALVAULT_API int TrialGetDefaultConfig(const char* softwareId,
                                                  TrialMode& modeOut,
                                                  uint32_t& limitOut) noexcept
{
    VMP_GUARD("TrialGetDefaultConfig");
    if (softwareId == nullptr) { modeOut = TrialMode::Days; limitOut = 14; HalSetLastError(HAL_OK); return HAL_OK; }
    std::string sid(softwareId);

    // prefix rules
    if (sid.rfind("VE", 0) == 0) { modeOut = TrialMode::Runs; limitOut = 30; HalSetLastError(HAL_OK); return HAL_OK; }
    if (sid.rfind("BDL", 0)== 0) { modeOut = TrialMode::Days; limitOut = 7;  HalSetLastError(HAL_OK); return HAL_OK; }

    // default
    modeOut = TrialMode::Days; limitOut = 14; HalSetLastError(HAL_OK); return HAL_OK;
}

// Optional: enable mixed trial policy (phase 1: runs, then fallback to days)
/**
 * 功能: 启用“混合试用”: 先给 runsFirst 次, 用尽后再给 daysAfter 天。
 * 用法: TrialConfigureMixed(5, 7);
 * 注意: 需先 TrialInitialize; 对已存在文档会就地更新并 bump seqNo。
 */
extern "C" HALVAULT_API int TrialConfigureMixed(uint32_t runsFirst, uint32_t daysAfter) noexcept
{
    VMP_GUARD("TrialConfigureMixed");
    if (!g_inited) { HalSetLastError(HAL_E_INVALIDARG); return HAL_E_INVALIDARG; }
    auto mtx = AcquireNamedMutex(g_sidMd5Lower);
    TrialDocV2 d{}; bool has = LoadBestDoc(g_sid, g_sidMd5Lower, d);
    if (!has) {
        // initialize minimal doc
        uint64_t now = UtcNowSec();
        d.version = kFileVersion; d.mode=(uint8_t)g_mode; d.frozen=0; d.reserved0=0; d.limit=g_limit;
        d.installUnix=now; d.maxSeenUnix=now; d.daysAccrued=0; d.lastDayIndex=DayIndex(now);
        d.runsUsed=0; d.seqNo=1; d.reserved1=0;
    }
    d.reserved0 |= kFlagMixed;
    d.reserved1 = PackMixed(runsFirst, daysAfter);
    d.seqNo += 1;
    if (!SaveDocAll(g_sid, g_sidMd5Lower, d)) { HalSetLastError(HAL_E_IO); return HAL_E_IO; }
    HalSetLastError(HAL_OK); return HAL_OK;
}

// =====================================================================================
//                              ADAPTER / MIGRATION NOTES
// =====================================================================================
// 1) If your header declares different function names or extra parameters,
//    wrap them to call TrialInitialize / TrialEvaluate / TrialConsumeOneRun.
// 2) Migration from your old v1 container:
//    - (Optional) Define HALVAULT_ENABLE_V1_MIGRATION and implement DecryptDocV1() below.
//      At load time, if no v2 replicas are found, attempt to read legacy v1, convert to v2,
