// TrialManager.secure.cpp — drop-in hardened trial manager
// Features: AES-GCM(AEAD) with AAD bound to softwareId, majority-vote multi-replica storage,
// time rollback freeze, monotonic day accrual, cross-process named mutex, 64-bit time.
// Compile: requires Crypto++ (aes.h, gcm.h, filters.h, osrng.h, sha.h, md5.h)
//
// NOTE:
// - Keep your existing "TrialManager.hpp" API. If signatures differ, see the "ADAPTER" section near the bottom.
// - This file is self-contained aside from Win32 and Crypto++.
//
// Copyright (c) 2025

#include "pch.h"
#include "TrialManager.hpp"
#include "halvault_error.h"
#include "hwid.hpp"
// Optional: if not present, comment out and let fallback RAII below be used.
#include "UniqueHandle.h"

#include <windows.h>
#include <shlobj.h>     // SHGetKnownFolderPath
#include <strsafe.h>
#include <wincrypt.h>

#include <cstdint>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <cwchar>
#include <string>
#include <vector>
#include <array>
#include <mutex>
#include <algorithm>
#include <initializer_list>

// == Crypto++ ==
#include <cryptopp/aes.h>
#include <cryptopp/gcm.h>
#include <cryptopp/filters.h>
#include <cryptopp/osrng.h>
#include <cryptopp/sha.h>
#include <cryptopp/md5.h>

#ifndef VMP_GUARD
#define VMP_GUARD(x)
#endif
#ifndef VMP_BLOCK
#define VMP_BLOCK(x)
#endif

// ---- HALVAULT fallback definitions (adjust/remove if your headers provide them) ----
#ifndef HALVAULT_API
#define HALVAULT_API __declspec(dllexport)
#endif

#ifndef HAL_OK
#define HAL_OK 0
#endif
#ifndef HAL_E_INVALIDARG
#define HAL_E_INVALIDARG 0x80070057
#endif
#ifndef HAL_E_TRIAL_EXPIRED
#define HAL_E_TRIAL_EXPIRED 0xA0010001
#endif
#ifndef HAL_E_TRIAL_FROZEN
#define HAL_E_TRIAL_FROZEN  0xA0010002
#endif
#ifndef HAL_E_IO
#define HAL_E_IO            0xA0010003
#endif
#ifndef HAL_E_CRYPTO
#define HAL_E_CRYPTO        0xA0010004
#endif
#ifndef HalSetLastError
static inline void HalSetLastError(int) {}
#endif

// ---- Minimal RAII for HANDLE if UniqueHandle is unavailable ----
#ifndef UNIQUEHANDLE_H
struct HandleCloser {
    void operator()(HANDLE h) const noexcept {
        if (h && h != INVALID_HANDLE_VALUE) ::CloseHandle(h);
    }
};
using UniqueHandle = std::unique_ptr<std::remove_pointer<HANDLE>::type, HandleCloser>;
static UniqueHandle make_unique_handle(HANDLE h) { return UniqueHandle(h); }
#else
// If you already have UniqueHandle.h with make_unique_handle, use it.
#endif

// ---- Public types (align with your header) ----
enum class TrialMode : uint8_t { Days = 0, Runs = 1 };

// ---- Internal constants ----
static constexpr char   kFileMagic[4] = {'T','R','I','A'};
static constexpr uint8_t kFileVersion = 2;           // encrypted container format version
static constexpr uint32_t kRollbackSlackSec = 300;   // 5min tolerance
static constexpr wchar_t kProductFolder[] = L"HALVault";

// ---- Globals (kept minimal) ----
static std::mutex      g_mutex;         // in-process
static bool            g_inited = false;
static std::string     g_sid;           // softwareId
static std::wstring    g_sidMd5Lower;   // for mutex & folder
static TrialMode       g_mode = TrialMode::Days;
static uint32_t        g_limit = 14;

// ---- Helpers: UTF16/UTF8 ----
static std::wstring Utf8ToWide(const std::string& s) {
    if (s.empty()) return std::wstring();
    int n = ::MultiByteToWideChar(CP_UTF8, 0, s.c_str(), (int)s.size(), nullptr, 0);
    std::wstring out; out.resize(n);
    ::MultiByteToWideChar(CP_UTF8, 0, s.c_str(), (int)s.size(), &out[0], n);
    return out;
}

// ---- Helpers: MD5 lower hex for mutex/folder ----
static std::wstring Md5LowerHex(const std::string& s) {
    CryptoPP::Weak::MD5 md5;
    md5.Update(reinterpret_cast<const CryptoPP::byte*>(s.data()), (size_t)s.size());
    CryptoPP::byte d[CryptoPP::Weak::MD5::DIGESTSIZE]{};
    md5.Final(d);
    static const wchar_t* hex = L"0123456789abcdef";
    std::wstring out; out.resize(32);
    for (int i=0;i<16;++i){ out[i*2+0]=hex[(d[i]>>4)&0xF]; out[i*2+1]=hex[d[i]&0xF]; }
    return out;
}

// ---- Helpers: time ----
static inline uint64_t UtcNowSec() noexcept {
    return static_cast<uint64_t>( ::_time64(nullptr) );
}
static inline uint32_t DayIndex(uint64_t sec) noexcept {
    return static_cast<uint32_t>( sec / 86400ULL );
}

// ---- Key derivation: SHA256(HWID || "HALVault" || softwareId) ----
static void DeriveKey(std::array<uint8_t,32>& outKey, const std::string& sid) {
    VMP_GUARD("Trial_DeriveKey");
    std::string hw = hwid::GetHWID();
    std::string material;
    material.reserve(hw.size()+sid.size()+8);
    material.append(hw);
    material.append("HALVault");
    material.append(sid);
    CryptoPP::SHA256 sha;
    sha.CalculateDigest(outKey.data(),
        reinterpret_cast<const CryptoPP::byte*>(material.data()),
        material.size());
}

// ---- Container AAD binder: magic + version + first 8 bytes of SID MD5 ----
static void BuildAAD(std::array<uint8_t,13>& aad, const std::string& sid) {
    aad[0]='T'; aad[1]='R'; aad[2]='I'; aad[3]='A'; aad[4]=kFileVersion;
    // SID MD5 (lower) -> take first 8 bytes of hex as ASCII for AAD (8 chars)
    auto md5hex = Md5LowerHex(sid);
    for (int i=0;i<8;++i) aad[5+i] = static_cast<uint8_t>( md5hex[i] & 0xFF );
}

// ---- Doc v2 (explicit serialization LE) ----
// We keep doc small and explicit to avoid ABI surprises.
struct TrialDocV2 {
    uint8_t  version;        // 2
    uint8_t  mode;           // TrialMode
    uint8_t  frozen;         // 0/1
    uint8_t  reserved0;
    uint32_t limit;          // total runs/days
    uint64_t installUnix;    // first install (UTC)
    uint64_t maxSeenUnix;    // anti-rollback horizon (UTC)
    uint32_t daysAccrued;    // monotonic day counter
    uint32_t lastDayIndex;   // dayIndex of last accrual
    uint32_t runsUsed;       // used runs (for Runs mode)
    uint32_t seqNo;          // monotonic write counter
    uint64_t reserved1;      // future
};
// static size check (not ABI critical due to explicit serialization)
static_assert(sizeof(TrialDocV2)==56, "TrialDocV2 unexpected size");

// LE write/read
static void le32_push(std::vector<uint8_t>& v, uint32_t x){
    v.push_back(uint8_t(x&0xFF)); v.push_back(uint8_t((x>>8)&0xFF));
    v.push_back(uint8_t((x>>16)&0xFF)); v.push_back(uint8_t((x>>24)&0xFF));
}
static void le64_push(std::vector<uint8_t>& v, uint64_t x){
    for(int i=0;i<8;++i) v.push_back(uint8_t((x>>(i*8))&0xFF));
}
static uint32_t le32_read(const uint8_t* p){ return p[0]|(p[1]<<8)|(p[2]<<16)|(p[3]<<24); }
static uint64_t le64_read(const uint8_t* p){
    uint64_t x=0; for(int i=0;i<8;++i) x |= (uint64_t)p[i] << (i*8); return x;
}

static void Serialize(const TrialDocV2& d, std::vector<uint8_t>& out) {
    out.clear(); out.reserve(64);
    out.push_back(d.version);
    out.push_back(d.mode);
    out.push_back(d.frozen);
    out.push_back(d.reserved0);
    le32_push(out, d.limit);
    le64_push(out, d.installUnix);
    le64_push(out, d.maxSeenUnix);
    le32_push(out, d.daysAccrued);
    le32_push(out, d.lastDayIndex);
    le32_push(out, d.runsUsed);
    le32_push(out, d.seqNo);
    le64_push(out, d.reserved1);
}
static bool Deserialize(const std::vector<uint8_t>& in, TrialDocV2& d) {
    if (in.size() != 56) return false;
    const uint8_t* p = in.data();
    d.version     = p[0];
    d.mode        = p[1];
    d.frozen      = p[2];
    d.reserved0   = p[3];
    d.limit       = le32_read(p+4);
    d.installUnix = le64_read(p+8);
    d.maxSeenUnix = le64_read(p+16);
    d.daysAccrued = le32_read(p+24);
    d.lastDayIndex= le32_read(p+28);
    d.runsUsed    = le32_read(p+32);
    d.seqNo       = le32_read(p+36);
    d.reserved1   = le64_read(p+40);
    return d.version==kFileVersion;
}

// ---- AES-GCM encrypt/decrypt container ----
static bool EncryptDoc(const TrialDocV2& doc, std::vector<uint8_t>& out, const std::string& sid) {
    VMP_BLOCK("Trial_EncryptDoc");
    std::array<uint8_t,32> key{}; DeriveKey(key, sid);
    CryptoPP::AutoSeededRandomPool prng;
    std::array<uint8_t,12> iv{}; prng.GenerateBlock(iv.data(), iv.size());
    std::array<uint8_t,13> aad{}; BuildAAD(aad, sid);

    std::vector<uint8_t> plain; Serialize(doc, plain);

    CryptoPP::GCM<CryptoPP::AES>::Encryption enc;
    enc.SetKeyWithIV(key.data(), key.size(), iv.data(), iv.size());

    std::string cipher;
    try {
        CryptoPP::AuthenticatedEncryptionFilter aef(
            enc, new CryptoPP::StringSink(cipher));
        aef.ChannelPut(CryptoPP::AAD_CHANNEL, aad.data(), aad.size());
        aef.ChannelMessageEnd(CryptoPP::AAD_CHANNEL);
        aef.Put(plain.data(), plain.size());
        aef.MessageEnd();
    } catch (...) { return false; }

    out.clear(); out.reserve(4+1+12+cipher.size());
    out.insert(out.end(), kFileMagic, kFileMagic+4);
    out.push_back(kFileVersion);
    out.insert(out.end(), iv.begin(), iv.end());
    out.insert(out.end(), cipher.begin(), cipher.end());
    return true;
}

static bool DecryptDoc(const std::vector<uint8_t>& in, TrialDocV2& outDoc, const std::string& sid) {
    if (in.size() < 4+1+12+16) return false;
    if (std::memcmp(in.data(), kFileMagic, 4)!=0) return false;
    if (in[4]!=kFileVersion) return false;
    const uint8_t* iv = in.data()+5;
    const uint8_t* c  = in.data()+5+12;
    size_t clen = in.size() - (5+12);

    std::array<uint8_t,32> key{}; DeriveKey(key, sid);
    std::array<uint8_t,13> aad{}; BuildAAD(aad, sid);

    CryptoPP::GCM<CryptoPP::AES>::Decryption dec;
    dec.SetKeyWithIV(key.data(), key.size(), iv, 12);

    std::string plain;
    try {
        CryptoPP::AuthenticatedDecryptionFilter adf(
            dec, new CryptoPP::StringSink(plain),
            CryptoPP::AuthenticatedDecryptionFilter::THROW_EXCEPTION);
        adf.ChannelPut(CryptoPP::AAD_CHANNEL, aad.data(), aad.size());
        adf.ChannelMessageEnd(CryptoPP::AAD_CHANNEL);
        adf.Put(c, clen);
        adf.MessageEnd();
    } catch (...) { return false; }

    std::vector<uint8_t> buf(plain.begin(), plain.end());
    return Deserialize(buf, outDoc);
}

// ---- Storage targets ----
struct Target { std::wstring path; std::wstring ads; };

static std::wstring GetKnownFolder(REFKNOWNFOLDERID id) {
    PWSTR p = nullptr; std::wstring out;
    if (SUCCEEDED(::SHGetKnownFolderPath(id, KF_FLAG_DEFAULT, nullptr, &p))) {
        out.assign(p); ::CoTaskMemFree(p);
    }
    return out;
}
static std::wstring Join(const std::wstring& a, const std::wstring& b){
    if (a.empty()) return b; if (b.empty()) return a;
    wchar_t sep = L'\\';
    if (a.back()==sep) return a+b;
    return a + sep + b;
}
static bool EnsureDir(const std::wstring& dir) {
    DWORD attr = ::GetFileAttributesW(dir.c_str());
    if (attr!=INVALID_FILE_ATTRIBUTES && (attr & FILE_ATTRIBUTE_DIRECTORY)) return true;
    return ::SHCreateDirectoryExW(nullptr, dir.c_str(), nullptr) == ERROR_SUCCESS;
}

static std::vector<Target> BuildTargets(const std::wstring& sidMd5Lower) {
    std::vector<Target> t;
    std::wstring base1 = Join(GetKnownFolder(FOLDERID_ProgramData), Join(kProductFolder, sidMd5Lower));
    std::wstring base2 = Join(GetKnownFolder(FOLDERID_LocalAppData), Join(kProductFolder, sidMd5Lower));
    EnsureDir(base1); EnsureDir(base2);

    // Primary files
    t.push_back({ Join(base1, L"trial.bin"), L"" });
    t.push_back({ Join(base1, L"trial.bin"), L":alt" }); // ADS on same file
    t.push_back({ Join(base2, L"trial.bin"), L"" });
    t.push_back({ Join(base2, L"trial.bin"), L":alt" });
    return t;
}

static bool WriteAllTargets(const std::vector<uint8_t>& blob, const std::vector<Target>& t) {
    bool any = false;
    for (const auto& x : t) {
        std::wstring p = x.path + x.ads;
        HANDLE h = ::CreateFileW(p.c_str(), GENERIC_WRITE, FILE_SHARE_READ, nullptr,
                                 CREATE_ALWAYS, FILE_ATTRIBUTE_HIDDEN|FILE_ATTRIBUTE_NOT_CONTENT_INDEXED, nullptr);
        if (h==INVALID_HANDLE_VALUE) continue;
        DWORD wr=0; BOOL ok = ::WriteFile(h, blob.data(), (DWORD)blob.size(), &wr, nullptr);
        ::FlushFileBuffers(h);
        ::CloseHandle(h);
        if (ok && wr == blob.size()) any = true;
    }
    return any;
}

static bool ReadOne(const Target& t, std::vector<uint8_t>& out) {
    std::wstring p = t.path + t.ads;
    HANDLE h = ::CreateFileW(p.c_str(), GENERIC_READ, FILE_SHARE_READ|FILE_SHARE_WRITE, nullptr,
                             OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (h==INVALID_HANDLE_VALUE) return false;
    DWORD sz = ::GetFileSize(h, nullptr);
    if (sz==INVALID_FILE_SIZE || sz==0 || sz> (10*1024*1024)) { ::CloseHandle(h); return false; }
    out.resize(sz);
    DWORD rd=0; BOOL ok = ::ReadFile(h, out.data(), sz, &rd, nullptr);
    ::CloseHandle(h);
    return ok && rd==sz;
}

static bool MajoritySelect(const std::vector<TrialDocV2>& docs, size_t& pick) {
    // Find exact duplicates by key fields; else choose max seqNo
    if (docs.empty()) return false;
    for (size_t i=0;i<docs.size();++i) {
        int count=1;
        for (size_t j=i+1;j<docs.size();++j) {
            const auto& a=docs[i], &b=docs[j];
            if (a.seqNo==b.seqNo &&
                a.maxSeenUnix==b.maxSeenUnix &&
                a.daysAccrued==b.daysAccrued &&
                a.runsUsed==b.runsUsed &&
                a.installUnix==b.installUnix) {
                ++count;
                if (count>=2){ pick=i; return true; }
            }
        }
    }
    // fallback: highest seqNo
    uint32_t bestSeq=0; size_t idx=0;
    for (size_t i=0;i<docs.size();++i){
        if (docs[i].seqNo>=bestSeq){ bestSeq=docs[i].seqNo; idx=i;}
    }
    pick = idx;
    return true;
}

static bool LoadBestDoc(const std::string& sid, const std::wstring& sidMd5Lower, TrialDocV2& out) {
    auto targets = BuildTargets(sidMd5Lower);
    std::vector<TrialDocV2> valid;
    for (auto& t : targets) {
        std::vector<uint8_t> blob;
        if (!ReadOne(t, blob)) continue;
        TrialDocV2 d{};
        if (!DecryptDoc(blob, d, sid)) continue;
        if (d.version!=kFileVersion) continue;
        valid.push_back(d);
    }
    if (valid.empty()) return false;
    size_t idx=0; MajoritySelect(valid, idx);
    out = valid[idx];
    return true;
}

static bool SaveDocAll(const std::string& sid, const std::wstring& sidMd5Lower, const TrialDocV2& doc) {
    std::vector<uint8_t> blob;
    if (!EncryptDoc(doc, blob, sid)) return false;
    auto targets = BuildTargets(sidMd5Lower);
    return WriteAllTargets(blob, targets);
}

// ---- Named mutex for cross-process ----
static UniqueHandle AcquireNamedMutex(const std::wstring& sidMd5Lower) {
    std::wstring name = L"Global\\HALVault_" + sidMd5Lower;
    HANDLE h = ::CreateMutexW(nullptr, FALSE, name.c_str());
    if (!h) return UniqueHandle{};
    DWORD wait = ::WaitForSingleObject(h, 8000); // 8s upper bound
    if (wait==WAIT_OBJECT_0 || wait==WAIT_ABANDONED) {
        return make_unique_handle(h);
    }
    ::CloseHandle(h);
    return UniqueHandle{};
}

// ---- Core evaluation/update ----
static int CoreEvaluate(bool consumeRun, uint32_t* remainingOut, uint32_t* totalOut) {
    if (!g_inited) { HalSetLastError(HAL_E_INVALIDARG); return HAL_E_INVALIDARG; }

    auto mtx = AcquireNamedMutex(g_sidMd5Lower);
    // proceed even if mutex failed; worst case concurrent update

    TrialDocV2 d{};
    bool has = LoadBestDoc(g_sid, g_sidMd5Lower, d);

    uint64_t now = UtcNowSec();
    uint32_t today = DayIndex(now);

    if (!has) {
        // First install doc
        d.version = kFileVersion;
        d.mode    = (uint8_t)g_mode;
        d.frozen  = 0;
        d.reserved0=0;
        d.limit   = g_limit;
        d.installUnix = now;
        d.maxSeenUnix = now;
        d.daysAccrued = 0;
        d.lastDayIndex= today;
        d.runsUsed    = 0;
        d.seqNo       = 1;
        d.reserved1   = 0;
        if (!SaveDocAll(g_sid, g_sidMd5Lower, d)) { HalSetLastError(HAL_E_IO); return HAL_E_IO; }
    }

    // Anti-rollback detection
    if (now + 1 < d.maxSeenUnix) { // if clock clearly moved back
        if ( (d.maxSeenUnix - now) > kRollbackSlackSec ) {
            d.frozen = 1;
            // do not lower maxSeenUnix
            SaveDocAll(g_sid, g_sidMd5Lower, d);
            if (remainingOut) *remainingOut = 0;
            if (totalOut) *totalOut = d.limit;
            HalSetLastError(HAL_E_TRIAL_FROZEN);
            return HAL_E_TRIAL_FROZEN;
        }
    }

    // Advance horizon when time moves forward normally
    if (now > d.maxSeenUnix) d.maxSeenUnix = now;
    if (d.frozen && now >= d.maxSeenUnix) d.frozen = 0; // thaw if recovered

    uint32_t remaining=0;
    if ((TrialMode)d.mode == TrialMode::Days) {
        if (today > d.lastDayIndex) {
            uint32_t delta = today - d.lastDayIndex;
            // Clamp: avoid overflow
            if (UINT32_MAX - d.daysAccrued < delta) d.daysAccrued = UINT32_MAX;
            else d.daysAccrued += delta;
            d.lastDayIndex = today;
        }
        remaining = (d.limit > d.daysAccrued) ? (d.limit - d.daysAccrued) : 0;
    } else {
        // Runs mode
        if (consumeRun) {
            if (d.runsUsed < d.limit) {
                d.runsUsed += 1;
            }
        }
        remaining = (d.limit > d.runsUsed) ? (d.limit - d.runsUsed) : 0;
    }

    // bump seqNo and persist even on expiration or frozen (except we already saved on frozen above)
    d.seqNo += 1;
    if (!SaveDocAll(g_sid, g_sidMd5Lower, d)) { HalSetLastError(HAL_E_IO); return HAL_E_IO; }

    if (remainingOut) *remainingOut = remaining;
    if (totalOut) *totalOut = d.limit;

    if (d.frozen) { HalSetLastError(HAL_E_TRIAL_FROZEN); return HAL_E_TRIAL_FROZEN; }
    if (remaining==0) { HalSetLastError(HAL_E_TRIAL_EXPIRED); return HAL_E_TRIAL_EXPIRED; }
    HalSetLastError(HAL_OK);
    return HAL_OK;
}

// =====================================================================================
//                                      API
// =====================================================================================

extern "C" HALVAULT_API int TrialInitialize(const char* softwareId,
                                            TrialMode mode, uint32_t limit) noexcept
{
    VMP_GUARD("TrialInitialize");
    if (!softwareId) { HalSetLastError(HAL_E_INVALIDARG); return HAL_E_INVALIDARG; }
    std::lock_guard<std::mutex> lk(g_mutex);
    g_sid.assign(softwareId);
    g_mode  = mode;
    g_limit = (limit==0? 1 : limit);
    g_sidMd5Lower = Md5LowerHex(g_sid);
    g_inited = true;

    // Force-create doc if none
    uint32_t rem=0; return CoreEvaluate(false, &rem, nullptr);
}

extern "C" HALVAULT_API int TrialEvaluate(uint32_t* remainingOut,
                                          uint32_t* totalLimitOut) noexcept
{
    VMP_GUARD("TrialEvaluate");
    return CoreEvaluate(false, remainingOut, totalLimitOut);
}

extern "C" HALVAULT_API int TrialConsumeOneRun(uint32_t* remainingOut,
                                               uint32_t* totalLimitOut) noexcept
{
    VMP_GUARD("TrialConsumeOneRun");
    return CoreEvaluate(true, remainingOut, totalLimitOut);
}

// Default policy by softwareId prefix (adjust rules to your fleet)
extern "C" HALVAULT_API int TrialGetDefaultConfig(const char* softwareId,
                                                  TrialMode& modeOut,
                                                  uint32_t& limitOut) noexcept
{
    VMP_GUARD("TrialGetDefaultConfig");
    if (softwareId == nullptr) { modeOut = TrialMode::Days; limitOut = 14; HalSetLastError(HAL_OK); return HAL_OK; }
    std::string sid(softwareId);

    // prefix rules
    if (sid.rfind("VE", 0) == 0) { modeOut = TrialMode::Runs; limitOut = 30; HalSetLastError(HAL_OK); return HAL_OK; }
    if (sid.rfind("BDL", 0)== 0) { modeOut = TrialMode::Days; limitOut = 7;  HalSetLastError(HAL_OK); return HAL_OK; }

    // default
    modeOut = TrialMode::Days; limitOut = 14; HalSetLastError(HAL_OK); return HAL_OK;
}

// =====================================================================================
//                              ADAPTER / MIGRATION NOTES
// =====================================================================================
// 1) If your header declares different function names or extra parameters,
//    wrap them to call TrialInitialize / TrialEvaluate / TrialConsumeOneRun.
// 2) Migration from your old v1 container:
//    - Enable and implement a DecryptDocV1() that understands your legacy layout,
//      then convert into TrialDocV2 and SaveDocAll(). Guard with a macro
//      like HALVAULT_ENABLE_V1_MIGRATION so new builds remain lean.
// 3) Concurrency:
//    - Intra-process: g_mutex, inter-process: named mutex "Global\\HALVault_${md5(sid)}".
// 4) Storage:
//    - Four replicas: %ProgramData%\HALVault\{md5(sid)}\trial.bin (+ADS :alt) and
//                     %LocalAppData%\HALVault\{md5(sid)}\trial.bin (+ADS :alt).
//      Majority-vote on load, all refreshed on save.
// 5) Security:
//    - AES-GCM with AAD bound to magic+version+sid-md5[0..7]. Key = SHA256(HWID||"HALVault"||sid).
//    - Monotonic day accrual; rollback > 300s enters frozen state until wall-clock recovers.
