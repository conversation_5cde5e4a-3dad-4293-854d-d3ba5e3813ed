#pragma once

#include <cstdint>
#include <string>
#include "halvault_api.h"

// 试用模式：按天或按次数二选一
enum class TrialMode : uint8_t { Days = 0, Runs = 1 };

struct TrialStatus {
    bool       allowed{};        // 是否允许继续试用
    uint32_t   remaining{};      // Days=剩余天数；Runs=剩余次数
    uint32_t   runsUsed{};       // 已使用次数（仅 Runs 有意义）
    uint32_t   limit{};          // 限制值（天数或次数）
    TrialMode  mode{TrialMode::Days};
    uint32_t   nowUnix{};        // 当前 Unix 秒
    uint32_t   installUnix{};    // 首次安装时间
};

// 初始化试用系统
// softwareId: 例如 "VE00013"；内部以 MD5(softwareId) 作为前缀派生存储位置
// mode: Days 或 Runs；limit: 对应天数或次数
HALVAULT_API int TrialInitialize(const char* softwareId) noexcept;

// 评估当前状态（不消耗次数）
HALVAULT_API int TrialEvaluate(TrialStatus& out) noexcept;

// 消耗一次（仅在 Runs 模式有效；原子更新并持久化）
HALVAULT_API int TrialConsumeOne() noexcept;

// 通过软件ID获取默认试用配置（若未命中专用配置，则返回全局默认：Days, 14）
// 返回 HAL_OK；modeOut: Days/Runs；limitOut: 天数或次数
HALVAULT_API int TrialGetDefaultConfig(const char* softwareId, TrialMode& modeOut, uint32_t& limitOut) noexcept;


