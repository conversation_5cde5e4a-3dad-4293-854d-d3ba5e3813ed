// TrialManager.secure.cpp 鈥� 寮哄寲璇曠敤绠＄悊鍣�
// Features: AES-GCM(AEAD) with AAD bound to softwareId, majority-vote multi-replica storage,
// time rollback freeze, monotonic day accrual, cross-process named mutex, 64-bit time.
// Compile: requires Crypto++ (aes.h, gcm.h, filters.h, osrng.h, sha.h, md5.h)
//
// NOTE:
// - Keep your existing "TrialManager.hpp" API. If signatures differ, see the "ADAPTER" section near the bottom.
// - This file is self-contained aside from Win32 and Crypto++.
//
// Copyright (c) 2025

#include "pch.h"
#include "trialmanager.hpp"
#include "halvault_error.h"
#include "hwid.hpp"
// Optional: if not present, comment out and let fallback RAII below be used.
#include "UniqueHandle.h"
#include <cryptopp/modes.h>
#include "policy.h"

#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0601 // Windows 7
#endif
#include <windows.h>
#include <shlobj.h>     // SHGetKnownFolderPath
#include <wincrypt.h>

#include <cstdint>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <cwchar>
#include <string>
#include <vector>
#include <array>
#include <mutex>
#include <algorithm>
#include <unordered_map>

// == Crypto++ ==
#include <cryptopp/aes.h>
#include <cryptopp/gcm.h>
#include <cryptopp/filters.h>
#include <cryptopp/osrng.h>
#include <cryptopp/sha.h>
#include <cryptopp/md5.h>
#include <cryptopp/hmac.h>

#ifndef VMP_GUARD
#define VMP_GUARD(x)
#endif
#ifndef VMP_BLOCK
#define VMP_BLOCK(x)
#endif

// ---- HALVAULT fallback definitions (adjust/remove if your headers provide them) ----
#ifndef HALVAULT_API
#define HALVAULT_API __declspec(dllexport)
#endif

#ifndef HAL_OK
#define HAL_OK 0
#endif
#ifndef HAL_E_INVALIDARG
#define HAL_E_INVALIDARG 0x80070057
#endif
#ifndef HAL_E_TRIAL_EXPIRED
#define HAL_E_TRIAL_EXPIRED 0xA0010001
#endif
#ifndef HAL_E_TRIAL_FROZEN
#define HAL_E_TRIAL_FROZEN  0xA0010002
#endif
#ifndef HAL_E_IO
#define HAL_E_IO            0xA0010003
#endif
#ifndef HAL_E_CRYPTO
#define HAL_E_CRYPTO        0xA0010004
#endif
#ifndef HalSetLastError
static inline void HalSetLastError( int ) { }
#endif

// ---- Minimal RAII for HANDLE if UniqueHandle is unavailable ----
#ifndef UNIQUEHANDLE_H
struct HandleCloser {
    void operator()( HANDLE h ) const noexcept
        {
        if (h && h != INVALID_HANDLE_VALUE) ::CloseHandle( h );
        }
    };
using UniqueHandle = std::unique_ptr<std::remove_pointer<HANDLE>::type , HandleCloser>;
static UniqueHandle make_unique_handle( HANDLE h ) { return UniqueHandle( h ); }
#else
// If you already have UniqueHandle.h with make_unique_handle, use it.
#endif

// TrialMode 澹版槑鍦ㄥご鏂囦欢 trial_manager.hpp

// ---- Internal constants ----
static constexpr char   kFileMagic [ 4 ] = { 'T','R','I','A' };
static constexpr uint8_t kFileVersion = 2;           // encrypted container format version
static constexpr uint32_t kRollbackSlackSec = 300;   // 5min tolerance
static constexpr wchar_t kProductFolder [ ] = L"HALVault";
// ---- Size limits ----
static constexpr size_t   kMaxBlobSize = 10ull * 1024 * 1024; // 10 MB upper bound for registry/file blobs
// ---- Globals (kept minimal) ----
static std::mutex      g_mutex;         // in-process
static bool            g_inited = false;
static std::string     g_sid;           // softwareId
static std::wstring    g_sidMd5Lower;   // for mutex & folder
static TrialMode       g_mode = TrialMode::Days;
static uint32_t        g_limit = 14;


static void DerivePolicy( const std::string& sid , TrialMode& mode , uint32_t& limit ) noexcept;
// ---- Helpers: UTF16/UTF8 ----
/**
 * 鍔熻兘: 灏� UTF-8 瀛楃�︿覆杞�鎹�涓� UTF-16 (Windows 瀹藉瓧绗︿覆)銆�
 * 鐢ㄦ硶: auto w = Utf8ToWide("abc");
 * 娉ㄦ剰: 澶勭悊绌哄瓧绗︿覆; 鐢ㄤ簬璺ㄥ瓧绗﹂泦鐜�澧冧笅瀹藉瓧绗﹀�勭悊銆�
 */
static std::wstring Utf8ToWide( const std::string& s )
    {
    if (s.empty( )) return std::wstring( );
    int n = ::MultiByteToWideChar( CP_UTF8 , 0 , s.c_str( ) , ( int ) s.size( ) , nullptr , 0 );
    std::wstring out; out.resize( n );
    ::MultiByteToWideChar( CP_UTF8 , 0 , s.c_str( ) , ( int ) s.size( ) , &out [ 0 ] , n );
    return out;
    }

// ---- Helpers: MD5 lower hex for mutex/folder ----
/**
 * 鍔熻兘: 璁＄畻杈撳叆瀛楃�︿覆鐨� MD5 鍝堝笇 32 浣嶅皬鍐欏崄鍏�杩涘埗瀛楃�︿覆銆�
 * 鐢ㄦ硶: auto hex = Md5LowerHex(sid);
 * 娉ㄦ剰: 鐢ㄤ簬浜掓枼閿�/鐩�褰曞懡鍚嶇瓑闈炲畨鍏ㄥ満鏅�; 瀹夊叏鍔熻兘浣跨敤 AES-GCM 鍔犲瘑
 */
static std::wstring Md5LowerHex( const std::string& s )
    {
    CryptoPP::Weak1::MD5 md5;
    md5.Update( reinterpret_cast< const CryptoPP::byte* >( s.data( ) ) , ( size_t ) s.size( ) );
    CryptoPP::byte d [ CryptoPP::Weak1::MD5::DIGESTSIZE ] {};
    md5.Final( d );
    static const wchar_t* hex = L"0123456789abcdef";
    std::wstring out; out.resize( 32 );
    for (int i = 0; i < 16; ++i) { out [ i * 2 + 0 ] = hex [ ( d [ i ] >> 4 ) & 0xF ]; out [ i * 2 + 1 ] = hex [ d [ i ] & 0xF ]; }
    return out;
    }

// ---- Helpers: time ----
/**
 * 鍔熻兘: 鑾峰彇褰撳墠 UTC 鏃堕棿(绉�, 64 浣�)銆�
 * 鐢ㄦ硶: uint64_t now = UtcNowSec();
 * 娉ㄦ剰: 浣跨敤 _time64, Win7 鍏煎��; 渚濊禆浜庣郴缁熸椂閽熴€�
 */
static inline uint64_t UtcNowSec( ) noexcept
    {
    return static_cast< uint64_t >( ::_time64( nullptr ) );
    }
/**
 * 鍔熻兘: 灏� UTC 绉掓暟杞�鎹�涓鸿嚜鐒舵棩绱㈠紩(璺� Epoch 鐨� 86400s 璁℃暟)銆�
 * 鐢ㄦ硶: uint32_t di = DayIndex(UtcNowSec());
 * 娉ㄦ剰: 鐢ㄤ簬鎸夊ぉ璁¤垂缁熻��, 璺ㄦ椂鍖哄垏鎹㈡湁褰卞搷銆�
 */
static inline uint32_t DayIndex( uint64_t sec ) noexcept
    {
    return static_cast< uint32_t >( sec / 86400ULL );
    }

// ---- Key derivation: SHA256(HWID || "HALVault" || softwareId) ----
/**
 * 鍔熻兘: 娲剧敓鍔犲瘑瀵嗛挜: SHA256(HWID || "HALVault" || softwareId)銆�
 * 鐢ㄦ硶: std::array<uint8_t,32> k; DeriveKey(k, sid);
 * 娉ㄦ剰: 缁戝畾鍒拌�惧�囧拰浜у搧; 鏋勬垚瀹瑰櫒鍔犲瘑瀵嗛挜; HWID 瀹炵幇鐢遍」鐩�鎻愪緵銆�
 */
static void DeriveKey( std::array<uint8_t , 32>& outKey , const std::string& sid )
    {
    VMP_GUARD( "Trial_DeriveKey" );
    std::string hw = hwid::GetHWIDHex( );
    std::string material;
    material.reserve( hw.size( ) + sid.size( ) + 8 );
    material.append( hw );
    material.append( "HALVault" );
    material.append( sid );
    CryptoPP::SHA256 sha;
    sha.CalculateDigest( outKey.data( ) ,
        reinterpret_cast< const CryptoPP::byte* >( material.data( ) ) ,
        material.size( ) );
    }

// ---- Container AAD binder: magic + version + first 8 bytes of SID MD5 ----
/**
 * 鍔熻兘: 鏋勫缓 AEAD 鐨� AAD(闄勫姞璁よ瘉鏁版嵁): magic+version+SID-MD5鍓�8瀛楄妭銆�
 * 鐢ㄦ硶: BuildAAD(aad, sid);
 * 娉ㄦ剰: 闃叉�㈣法浜у搧/璺ㄧ増鏈�閲嶆斁; 缁戝畾瀵嗛挜涓婁笅鏂囥€�
 */
static void BuildAAD( std::array<uint8_t , 13>& aad , const std::string& sid )
    {
    aad [ 0 ] = 'T'; 
    aad [ 1 ] = 'R'; 
    aad [ 2 ] = 'I'; 
    aad [ 3 ] = 'A'; 
    aad [ 4 ] = kFileVersion;
    // SID MD5 (lower) -> take first 8 bytes of hex as ASCII for AAD (8 chars)
    auto md5hex = Md5LowerHex( sid );
    for (int i = 0; i < 8; ++i) {
        aad [ 5 + i ] = static_cast< uint8_t >( md5hex [ i ] & 0xFF );
        }
    }

// ---- Doc v2 (explicit serialization LE) ----
// We keep doc small and explicit to avoid ABI surprises.
struct TrialDocV2 {
    uint8_t  version;        // 2
    uint8_t  mode;           // TrialMode
    uint8_t  frozen;         // 0/1
    uint8_t  reserved0;
    uint32_t limit;          // total runs/days
    uint64_t installUnix;    // first install (UTC)
    uint64_t maxSeenUnix;    // anti-rollback horizon (UTC)
    uint32_t daysAccrued;    // monotonic day counter
    uint32_t lastDayIndex;   // dayIndex of last accrual
    uint32_t runsUsed;       // used runs (for Runs mode)
    uint32_t seqNo;          // monotonic write counter
    uint64_t reserved1;      // future
    };
// static size check (not ABI critical due to explicit serialization)
static_assert( sizeof( TrialDocV2 ) == 48 , "TrialDocV2 unexpected size" );

// LE write/read
static void le32_push( std::vector<uint8_t>& v , uint32_t x )
    {
    v.push_back( uint8_t( x & 0xFF ) ); 
    v.push_back( uint8_t( ( x >> 8 ) & 0xFF ) );
    v.push_back( uint8_t( ( x >> 16 ) & 0xFF ) ); 
    v.push_back( uint8_t( ( x >> 24 ) & 0xFF ) );
    }

static void le64_push( std::vector<uint8_t>& v , uint64_t x )
    {
    for (int i = 0; i < 8; ++i) 
        v.push_back( uint8_t( ( x >> ( i * 8 ) ) & 0xFF ) );
    }

static uint32_t le32_read( const uint8_t* p ) {
    return p [ 0 ] | ( p [ 1 ] << 8 ) | ( p [ 2 ] << 16 ) | ( p [ 3 ] << 24 );
    }

static uint64_t le64_read( const uint8_t* p )
    {
    uint64_t x = 0; 
    for (int i = 0; i < 8; ++i) 
        x |= ( uint64_t ) p [ i ] << ( i * 8 ); 

    return x;
    }

/**
 * 鍔熻兘: 灏� TrialDocV2 鎸夊皬绔�鏍煎紡搴忓垪鍖栬嚦瀛楄妭鏁扮粍銆�
 * 鐢ㄦ硶: Serialize(doc, buf);
 * 娉ㄦ剰: 鍥哄畾 48 瀛楄妭; 淇�鏀圭粨鏋勪綋闇€鍚屾�ユ洿鏂� Deserialize 鍜� static_assert銆�
 */
static void Serialize( const TrialDocV2& d , std::vector<uint8_t>& out )
    {
    out.clear( ); 
    out.reserve( 64 );
    out.push_back( d.version );
    out.push_back( d.mode );
    out.push_back( d.frozen );
    out.push_back( d.reserved0 );
    le32_push( out , d.limit );
    le64_push( out , d.installUnix );
    le64_push( out , d.maxSeenUnix );
    le32_push( out , d.daysAccrued );
    le32_push( out , d.lastDayIndex );
    le32_push( out , d.runsUsed );
    le32_push( out , d.seqNo );
    le64_push( out , d.reserved1 );
    }
/**
 * 鍔熻兘: 浠� 48 瀛楄妭灏忕��鏁版嵁鍙嶅簭鍒楀寲 TrialDocV2銆�
 * 鐢ㄦ硶: if(Deserialize(buf,d)) ...;
 * 娉ㄦ剰: 闀垮害涓嶄负 48 鐩存帴澶辫触; 鐗堟湰妫€鏌ラ渶鍖归厤 kFileVersion銆�
 */
static bool Deserialize( const std::vector<uint8_t>& in , TrialDocV2& d )
    {
    if (in.size( ) != 48) 
        return false;

    const uint8_t* p = in.data( );
    d.version = p [ 0 ];
    d.mode = p [ 1 ];
    d.frozen = p [ 2 ];
    d.reserved0 = p [ 3 ];
    d.limit = le32_read( p + 4 );
    d.installUnix = le64_read( p + 8 );
    d.maxSeenUnix = le64_read( p + 16 );
    d.daysAccrued = le32_read( p + 24 );
    d.lastDayIndex = le32_read( p + 28 );
    d.runsUsed = le32_read( p + 32 );
    d.seqNo = le32_read( p + 36 );
    d.reserved1 = le64_read( p + 40 );
    return d.version == kFileVersion;
    }

// ---- AES-GCM encrypt/decrypt container ----
/**
 * 鍔熻兘: 浣跨敤 AES-GCM 鍔犲瘑 TrialDocV2, 鏍煎紡: magic|version|IV|cipher+tag銆�
 * 鐢ㄦ硶: EncryptDoc(doc, bytes, sid);
 * 娉ㄦ剰: 闅忔満 96-bit IV; AAD 缁戝畾浜у搧; 澶辫触杩斿洖 false 涓嶆姏寮傚父銆�
 */
static bool EncryptDoc( const TrialDocV2& doc , std::vector<uint8_t>& out , const std::string& sid )
{
    VMP_BLOCK( "Trial_EncryptDoc" );

    // 参数验证
    if (sid.empty()) {
        return false;
    }

    try {
        // 1. 派生密钥和 AAD
        std::array<uint8_t, 32> key{};
        DeriveKey(key, sid);

        std::array<uint8_t, 13> aad{};
        BuildAAD(aad, sid);

        // 2. 生成随机 IV
        CryptoPP::AutoSeededRandomPool prng;
        std::array<uint8_t, 12> iv{};
        prng.GenerateBlock(iv.data(), iv.size());

        // 3. 序列化文档
        std::vector<uint8_t> plaintext;
        Serialize(doc, plaintext);

        // 验证序列化结果
        if (plaintext.size() != 48) {
            return false;
        }

        // 4. 设置 GCM 加密器
        CryptoPP::GCM<CryptoPP::AES>::Encryption enc;
        enc.SetKeyWithIV(key.data(), key.size(), iv.data(), iv.size());

        // 5. 执行加密
        std::string ciphertext;
        CryptoPP::AuthenticatedEncryptionFilter aef(
            enc,
            new CryptoPP::StringSink(ciphertext),
            false, // 不抛出异常，通过返回值检查
            -1     // 默认标签长度 (16 字节)
        );

        // 添加 AAD
        aef.ChannelPut(CryptoPP::AAD_CHANNEL, aad.data(), aad.size());
        aef.ChannelMessageEnd(CryptoPP::AAD_CHANNEL);

        // 加密明文
        aef.Put(plaintext.data(), plaintext.size());
        aef.MessageEnd();

        // 6. 构建最终输出格式: magic(4) + version(1) + IV(12) + ciphertext+tag
        const size_t totalSize = 4 + 1 + 12 + ciphertext.size();
        if (totalSize > kMaxBlobSize) {
            return false;
        }

        out.clear();
        out.reserve(totalSize);

        // 添加文件头
        out.insert(out.end(), kFileMagic, kFileMagic + 4);
        out.push_back(kFileVersion);

        // 添加 IV
        out.insert(out.end(), iv.begin(), iv.end());

        // 添加密文+标签
        out.insert(out.end(), ciphertext.begin(), ciphertext.end());

#ifdef _DEBUG
        OutputDebugStringA("[HALVault::EncryptDoc] Success: ");
        OutputDebugStringA(("plaintext=" + std::to_string(plaintext.size()) +
                           " ciphertext=" + std::to_string(ciphertext.size()) +
                           " total=" + std::to_string(out.size()) + "\n").c_str());
#endif

        return true;
    }
    catch (const CryptoPP::Exception& ex) {
#ifdef _DEBUG
        OutputDebugStringA("[HALVault::EncryptDoc] CryptoPP::Exception: ");
        OutputDebugStringA(ex.what());
        OutputDebugStringA("\n");
#endif
        return false;
    }
    catch (const std::exception& ex) {
#ifdef _DEBUG
        OutputDebugStringA("[HALVault::EncryptDoc] std::exception: ");
        OutputDebugStringA(ex.what());
        OutputDebugStringA("\n");
#endif
        return false;
    }
    catch (...) {
#ifdef _DEBUG
        OutputDebugStringA("[HALVault::EncryptDoc] Unknown exception\n");
#endif
        return false;
    }
}

/**
 * 鍔熻兘: 瑙ｅ瘑骞堕獙璇佹暟鎹�; 閫氳繃鍙嶅簭鍒楀寲涓� TrialDocV2銆�
 * 鐢ㄦ硶: if (DecryptDoc(bytes, doc, sid)) ...
 * 娉ㄦ剰: 鏍￠獙 magic/version; AAD+Tag 鏍￠獙澶辫触鐩存帴杩斿洖 false銆�
 */
static bool DecryptDoc( const std::vector<uint8_t>& in , TrialDocV2& outDoc , const std::string& sid )
    {
    if (in.size( ) < 4 + 1 + 12 + 16) 
        return false;

    if (std::memcmp( in.data( ) , kFileMagic , 4 ) != 0) 
        return false;

    if (in [ 4 ] != kFileVersion) 
        return false;


    // 参数验证
    if (sid.empty()) {
        return false;
    }

    // 检查总大小限制
    if (in.size() > kMaxBlobSize) {
        return false;
    }

    try {
        // 解析数据布局
        const uint8_t* iv = in.data() + 5;           // IV 位置
        const uint8_t* ciphertext = in.data() + 17;  // 密文位置 (5 + 12)
        const size_t ciphertextLen = in.size() - 17; // 密文+标签长度

        // 验证密文长度 (至少包含 16 字节标签)
        if (ciphertextLen < 16) {
            return false;
        }

        // 派生密钥和 AAD
        std::array<uint8_t, 32> key{};
        DeriveKey(key, sid);

        std::array<uint8_t, 13> aad{};
        BuildAAD(aad, sid);

        // 设置 GCM 解密器
        CryptoPP::GCM<CryptoPP::AES>::Decryption dec;
        dec.SetKeyWithIV(key.data(), key.size(), iv, 12);

        // 执行解密
        std::string plaintext;
        CryptoPP::AuthenticatedDecryptionFilter adf(
            dec,
            new CryptoPP::StringSink(plaintext),
            CryptoPP::AuthenticatedDecryptionFilter::THROW_EXCEPTION
        );

        // 添加 AAD
        adf.ChannelPut(CryptoPP::AAD_CHANNEL, aad.data(), aad.size());
        adf.ChannelMessageEnd(CryptoPP::AAD_CHANNEL);

        // 解密数据
        adf.Put(ciphertext, ciphertextLen);
        adf.MessageEnd();

        // 验证解密结果
        if (plaintext.size() != 48) {
#ifdef _DEBUG
            OutputDebugStringA("[HALVault::DecryptDoc] Invalid plaintext size: ");
            OutputDebugStringA(std::to_string(plaintext.size()).c_str());
            OutputDebugStringA(" (expected 48)\n");
#endif
            return false;
        }

        // 反序列化
        std::vector<uint8_t> plaintextBytes(plaintext.begin(), plaintext.end());
        bool deserializeResult = Deserialize(plaintextBytes, outDoc);

#ifdef _DEBUG
        if (deserializeResult) {
            OutputDebugStringA("[HALVault::DecryptDoc] Success: ");
            OutputDebugStringA(("input=" + std::to_string(in.size()) +
                               " ciphertext=" + std::to_string(ciphertextLen) +
                               " plaintext=" + std::to_string(plaintext.size()) + "\n").c_str());
        }
#endif

        return deserializeResult;
    }
    catch (const CryptoPP::Exception& ex) {
#ifdef _DEBUG
        OutputDebugStringA("[HALVault::DecryptDoc] CryptoPP::Exception: ");
        OutputDebugStringA(ex.what());
        OutputDebugStringA("\n");
#endif
        return false;
    }
    catch (const std::exception& ex) {
#ifdef _DEBUG
        OutputDebugStringA("[HALVault::DecryptDoc] std::exception: ");
        OutputDebugStringA(ex.what());
        OutputDebugStringA("\n");
#endif
        return false;
    }
    catch (...) {
#ifdef _DEBUG
        OutputDebugStringA("[HALVault::DecryptDoc] Unknown exception\n");
#endif
        return false;
    }
    }

// ---- Storage targets ----
struct Target { std::wstring path; std::wstring ads; };

/**
 * 鍔熻兘: 鑾峰彇宸茬煡鏂囦欢澶硅矾寰� (濡� LocalAppData/Roaming)銆�
 * 鐢ㄦ硶: auto p = GetKnownFolder(FOLDERID_LocalAppData);
 * 娉ㄦ剰: Win7 纭�淇�; 杩斿洖绌鸿〃绀哄け璐ャ€�
 */
static std::wstring GetKnownFolder( REFKNOWNFOLDERID id )
    {
    PWSTR p = nullptr; 
    std::wstring out;

    if (SUCCEEDED( ::SHGetKnownFolderPath( id , KF_FLAG_DEFAULT , nullptr , &p ) )) {
        out.assign( p ); 
        ::CoTaskMemFree( p );
        }
    return out;
    }
/**
 * 鍔熻兘: 瀵瑰弽鏂滄潬鍚堝苟涓や釜 Windows 璺�寰勭墖娈点€�
 * 鐢ㄦ硶: auto p = Join(base, L"trial.bin");
 * 娉ㄦ剰: 鑷�鍔ㄥ�勭悊灏鹃殢鍒嗛殧绗�; 閬垮厤鍙屾枩鏉犮€�
 */
static std::wstring Join( const std::wstring& a , const std::wstring& b )
    {
    if (a.empty( )) 
        return b; 

    if (b.empty( )) 
        return a;

    wchar_t sep = L'\\';
    if (a.back( ) == sep) 
        return a + b;
    return a + sep + b;
    }
/**
 * 鍔熻兘: 纭�淇濈洰褰曞瓨鍦�; 涓嶅瓨鍦ㄥ垯鍒涘缓銆�
 * 鐢ㄦ硶: EnsureDir(path);
 * 娉ㄦ剰: 使瀛樺湪 SHCreateDirectoryExW; 瀛樺湪 FAT/纭�淇濆瓨鍦ㄍ�纭�淇澬у瓨鍦�
 */
static bool EnsureDir( const std::wstring& dir )
    {
    DWORD attr = ::GetFileAttributesW( dir.c_str( ) );
    if (attr != INVALID_FILE_ATTRIBUTES && ( attr & FILE_ATTRIBUTE_DIRECTORY ))
        return true;

    return ::SHCreateDirectoryExW( nullptr , dir.c_str( ) , nullptr ) == ERROR_SUCCESS;
    }

// Local evidence log path in LocalAppData
/**
 * 鍔熻兘: 鑾峰彇褰撳墠鐢ㄦ埛 LocalAppData 涓嬬殑璇佹嵁鏃ュ織璺�寰勩€�
 * 鐢ㄦ硶: auto lp = LocalLogPath(md5);
 * 娉ㄦ剰: 鑷�鍔ㄧ‘淇濈洰褰曞瓨鍦�; 鏃ュ織鏀�鎸佽拷鍔犲啓鍏ャ€�
 */
static std::wstring LocalLogPath( const std::wstring& sidMd5Lower )
    {
    std::wstring base = Join( GetKnownFolder( FOLDERID_LocalAppData ) , Join( kProductFolder , sidMd5Lower ) );
    EnsureDir( base );
    return Join( base , L"trial.log" );
    }

/**
 * 鍔熻兘: 鏋勫缓鏁版嵁瀛樺偍鐩�鏍�(涓绘枃浠�+ADS, Local 鍜� Roaming)銆�
 * 鐢ㄦ硶: auto t = BuildTargets(md5);
 * 娉ㄦ剰: 鑻ユ枃浠剁郴缁熶笉鏀�鎸� ADS, 鍐欏叆鏃跺け璐ヤ絾涓嶅穿婧�; 鏀�鎸佸�氭暟鎶曠エ瀹归敊銆�
 */
static std::vector<Target> BuildTargets( const std::wstring& sidMd5Lower )
    {
    std::vector<Target> t;
    std::wstring base1 = Join( GetKnownFolder( FOLDERID_LocalAppData ) , Join( kProductFolder , sidMd5Lower ) );
    std::wstring base2 = Join( GetKnownFolder( FOLDERID_RoamingAppData ) , Join( kProductFolder , sidMd5Lower ) );
    EnsureDir( base1 ); EnsureDir( base2 );

    // Primary files
    t.push_back( { Join( base1, L"trial.bin" ), L"" } );
    t.push_back( { Join( base1, L"trial.bin" ), L":alt" } ); // ADS on same file
    t.push_back( { Join( base2, L"trial.bin" ), L"" } );
    t.push_back( { Join( base2, L"trial.bin" ), L":alt" } );
    return t;
    }

// ---- Registry replicas (HKCU) ----
/**
 * 鍔熻兘: 鍐欏叆 HKCU 娉ㄥ唽琛ㄤ簩杩涘埗鏁板€�(鍓�鏈�涔嬩竴)銆�
 * 鐢ㄦ硶: RegWriteBlob(md5, L"trial", bytes);
 * 娉ㄦ剰: 璺�寰勪负 HKCU\Software\HALVault\{md5}; 鏍囧噯鐢ㄦ埛鏉冮檺鍐欏叆銆�
 */

static bool RegWriteBlob( const std::wstring& sidMd5Lower , const wchar_t* valueName , const std::vector<uint8_t>& blob )
    {
    HKEY hKey = nullptr;
    std::wstring subkey = L"Software\\HALVault\\" + sidMd5Lower;
    LSTATUS s = ::RegCreateKeyExW( HKEY_CURRENT_USER ,
        subkey.c_str( ) , 0 , nullptr , 0 , KEY_SET_VALUE , nullptr , &hKey , nullptr );

    if (s != ERROR_SUCCESS) 
        return false;

    s = ::RegSetValueExW( hKey , valueName , 0 , REG_BINARY ,
        reinterpret_cast< const BYTE* >( blob.data( ) ) ,
        ( DWORD ) blob.size( ) );

    ::RegCloseKey( hKey );
    return s == ERROR_SUCCESS;
    }

static bool RegReadBlob( const std::wstring& sidMd5Lower , const wchar_t* valueName , std::vector<uint8_t>& out )
    {
    HKEY hKey = nullptr;
    std::wstring subkey = L"Software\\HALVault\\" + sidMd5Lower;
    LSTATUS s = ::RegOpenKeyExW( HKEY_CURRENT_USER , subkey.c_str( ) , 0 , KEY_QUERY_VALUE , &hKey );

    if (s != ERROR_SUCCESS) 
        return false;

    DWORD type = 0 , size = 0;

    s = ::RegQueryValueExW( hKey , valueName , nullptr , &type , nullptr , &size );
    if (s != ERROR_SUCCESS || type != REG_BINARY || size == 0 || size > kMaxBlobSize) { 
        ::RegCloseKey( hKey );
        return false; 
        }

    out.resize( size );
    s = ::RegQueryValueExW( hKey , valueName , nullptr , &type , out.data( ) , &size );
    ::RegCloseKey( hKey );
    return s == ERROR_SUCCESS;
    }




/**
 * 鍔熻兘: RegWriteBlob 鐨勫寘瑁呭櫒, 淇濈暀鏈�鏉ュ垏鎹㈠疄鐜般€�
 * 鐢ㄦ硶: RegWriteBlob2(md5, L"alt", bytes);
 * 娉ㄦ剰: 涓� RegWriteBlob 涓虹瓑浠枫€�
 */
static bool RegWriteBlob2( const std::wstring& sidMd5Lower , const wchar_t* valueName , const std::vector<uint8_t>& blob )
    {
    return RegWriteBlob( sidMd5Lower , valueName , blob );
    }
/**
 * 鍔熻兘: RegReadBlob 鐨勫寘瑁呭櫒銆�
 * 鐢ㄦ硶: RegReadBlob2(md5, L"alt", out);
 * 娉ㄦ剰: 涓� RegReadBlob 涓虹瓑浠枫€�
 */
static bool RegReadBlob2( const std::wstring& sidMd5Lower , const wchar_t* valueName , std::vector<uint8_t>& out )
    {
    return RegReadBlob( sidMd5Lower , valueName , out );
    }
/**
 * 鍔熻兘: 鍚屾椂鍐欏叆 trial/alt 涓や釜娉ㄥ唽琛ㄥ€�; 浠讳竴鎴愬姛鍗充负鎴愬姛銆�
 * 鐢ㄦ硶: RegWriteBoth(md5, bytes);
 * 娉ㄦ剰: 澧炲姞瀹归敊鎬�, 鏀�鎸佹枃浠跺瓨鍌ㄥ悓鏍峰彲璇荤殑澶氭暟鎶曠エ銆�
 */
static bool RegWriteBoth( const std::wstring& sidMd5Lower , const std::vector<uint8_t>& blob )
    {
    bool a = RegWriteBlob2( sidMd5Lower , L"trial" , blob );
    bool b = RegWriteBlob2( sidMd5Lower , L"alt" , blob );
    return a || b;
    }

// ---- Authenticated & trimmed evidence log (LocalAppData) ----
#pragma pack(push,1)
struct LogRecV1 { uint32_t tag; uint32_t seqNo; uint64_t maxSeen; uint32_t daysAccrued; };
struct LogRecV2 { uint32_t tag; uint32_t seqNo; uint64_t maxSeen; uint32_t daysAccrued; uint8_t mac [ 16 ]; };
#pragma pack(pop)
static constexpr uint32_t kLogTagV1 = ( 'T' ) | ( 'R' << 8 ) | ( 'L' << 16 ) | ( 'G' << 24 ); // 'TRLG'
static constexpr uint32_t kLogTagV2 = ( 'T' ) | ( 'L' << 8 ) | ( 'G' << 16 ) | ( '2' << 24 ); // 'TLG2'
static constexpr size_t kLogMaxRecords = 4096;
static constexpr size_t kLogTrimKeep = 1024;

/**
 * 鍔熻兘: 娲剧敓鏃ュ織 HMAC 瀵嗛挜(浠庝富鍔犲瘑瀵嗛挜鍐嶅姞鐩�)銆�
 * 鐢ㄦ硶: DeriveLogKey(k, sid);
 * 娉ㄦ剰: 闃叉��涓诲瘑閽ユ硠闇�, 闃叉�㈠瘑閽ュ�嶇敤鏀诲嚮銆�
 */
static void DeriveLogKey( std::array<uint8_t , 32>& key , const std::string& sid )
    {
    std::array<uint8_t , 32> base {}; 
    DeriveKey( base , sid );
    CryptoPP::SHA256 sha;
    const char salt [ ] = "HALVault-LOGv1";
    std::string material( reinterpret_cast< const char* >( base.data( ) ) , base.size( ) );
    material.append( salt , sizeof( salt ) - 1 );
    sha.CalculateDigest( key.data( ) , reinterpret_cast< const CryptoPP::byte* >( material.data( ) ) , material.size( ) );
    }

/**
 * 鍔熻兘: 褰撴棩蹇楄�板綍鏁拌秴杩囬槇鍊兼椂淇�鍓�淇濈暀鏈�灏炬渶鏂扮殑 kLogTrimKeep 鏉＄洰銆�
 * 鐢ㄦ硶: LogTrimIfNeeded(md5);
 * 娉ㄦ剰: 鑷�鍔ㄨ瘑鍒� V1/V2 璁板綍灏哄��; 鏀�鎸佸氨鍦颁慨鍓�銆�
 */
static void LogTrimIfNeeded( const std::wstring& sidMd5Lower )
    {
    std::wstring p = LocalLogPath( sidMd5Lower );
    HANDLE hr = ::CreateFileW( p.c_str( ) , GENERIC_READ | GENERIC_WRITE , FILE_SHARE_READ , nullptr , OPEN_EXISTING , FILE_ATTRIBUTE_NORMAL , nullptr );
    if (hr == INVALID_HANDLE_VALUE) 
        return;

    LARGE_INTEGER li {};
    if (!::GetFileSizeEx( hr , &li )) {
        ::CloseHandle( hr ); 
        return;
        }

    ULONGLONG sz = ( ULONGLONG ) li.QuadPart;
    if (sz == 0) { 
        ::CloseHandle( hr );
        return; 
        }

    auto trim_tail = [ & ]( size_t recSize , size_t n )
        {
        if (n <= kLogMaxRecords)
            return;

        size_t keep = ( std::min ) ( kLogTrimKeep , n );
        std::vector<uint8_t> buf( keep * recSize );
        LARGE_INTEGER off {}; 
        off.QuadPart = ( LONGLONG ) ( ( n - keep ) * recSize );
        ::SetFilePointerEx( hr , off , nullptr , FILE_BEGIN );
        DWORD rd = 0; 
        ::ReadFile( hr , buf.data( ) , ( DWORD ) buf.size( ) , &rd , nullptr );
        ::SetFilePointer( hr , 0 , nullptr , FILE_BEGIN );
        ::SetEndOfFile( hr );
        DWORD wr = 0; 
        ::WriteFile( hr , buf.data( ) , ( DWORD ) buf.size( ) , &wr , nullptr );
        };

    if (sz % sizeof( LogRecV2 ) == 0) {
        size_t n = ( size_t ) ( sz / sizeof( LogRecV2 ) );
        trim_tail( sizeof( LogRecV2 ) , n );
        }
    else if (sz % sizeof( LogRecV1 ) == 0) {
        size_t n = ( size_t ) ( sz / sizeof( LogRecV1 ) );
        trim_tail( sizeof( LogRecV1 ) , n );
        }
    ::CloseHandle( hr );
    }

/**
 * 鍔熻兘: 浠� V2 璁板綍鏍煎紡杩藉姞鍐欏叆鏃ュ織, 鍖呭惈鍙�楠岃瘉 HMAC 鏍￠獙鐮併€�
 * 鐢ㄦ硶: LogAppend2(md5, doc, sid);
 * 娉ㄦ剰: 澶辫触闈欓粯, 涓嶅奖鍝嶄富娴佺▼; 鏈�灏句細瑙﹀彂淇�鍓�銆�
 */
static void LogAppend2( const std::wstring& sidMd5Lower , const TrialDocV2& d , const std::string& sid )
    {
    std::wstring p = LocalLogPath( sidMd5Lower );
    HANDLE h = ::CreateFileW( p.c_str( ) , FILE_APPEND_DATA , FILE_SHARE_READ , nullptr ,
        OPEN_ALWAYS , FILE_ATTRIBUTE_HIDDEN | FILE_ATTRIBUTE_NOT_CONTENT_INDEXED , nullptr );
    if (h == INVALID_HANDLE_VALUE) 
        return;
    LogRecV2 rec {};
    rec.tag = kLogTagV2; 
    rec.seqNo = d.seqNo; 
    rec.maxSeen = d.maxSeenUnix; 
    rec.daysAccrued = d.daysAccrued;
    std::array<uint8_t , 32> key {}; 
    DeriveLogKey( key , sid );

    CryptoPP::HMAC<CryptoPP::SHA256> hmac( key.data( ) , key.size( ) );
    hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &rec.tag ) , sizeof( rec.tag ) );
    hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &rec.seqNo ) , sizeof( rec.seqNo ) );
    hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &rec.maxSeen ) , sizeof( rec.maxSeen ) );
    hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &rec.daysAccrued ) , sizeof( rec.daysAccrued ) );
    auto md5hex = Md5LowerHex( sid );
    hmac.Update( reinterpret_cast< const CryptoPP::byte* >( md5hex.data( ) ) , 8 * sizeof( wchar_t ) );
    std::array<uint8_t , 32> full {}; 
    hmac.Final( full.data( ) );
    std::memcpy( rec.mac , full.data( ) , 16 );
    DWORD wr = 0; 
    ::WriteFile( h , &rec , sizeof( rec ) , &wr , nullptr );
    ::CloseHandle( h );
    LogTrimIfNeeded( sidMd5Lower );
    }

/**
 * 鍔熻兘: 璇诲彇鑱氬悎鍚庢棩蹇楄瘉鎹�(鏈€澶� seq/maxSeen/days), 鎸囩ず鏄�鍚﹂€氳繃 HMAC 楠岃瘉銆�
 * 鐢ㄦ硶: LogReadEvidence2(md5, sid, seq, maxSeen, days, authed);
 * 娉ㄦ剰: 瀹屽叏涓鸿€� V1 璁板綍, hasAuth=false, 浣嗕粛杩斿洖鑱氬悎鍘嗗彶鐨勫ぇ鑷村€笺€�
 */
static void LogReadEvidence2( const std::wstring& sidMd5Lower , const std::string& sid ,
    uint32_t& seqMax , uint64_t& maxSeenMax , uint32_t& daysMax , bool& hasAuth )
    {
    seqMax = 0; 
    maxSeenMax = 0; 
    daysMax = 0; 
    hasAuth = false;
    std::wstring p = LocalLogPath( sidMd5Lower );
    HANDLE h = ::CreateFileW( p.c_str( ) , GENERIC_READ , FILE_SHARE_READ | FILE_SHARE_WRITE , nullptr ,
        OPEN_EXISTING , FILE_ATTRIBUTE_NORMAL , nullptr );

    if (h == INVALID_HANDLE_VALUE)
        return;
    LARGE_INTEGER li {};
    if (!::GetFileSizeEx( h , &li )) {
        ::CloseHandle( h );
        return; 
        }
    ULONGLONG sz = ( ULONGLONG ) li.QuadPart;
    if (sz == 0) { 
        ::CloseHandle( h );
        return; 
        }

    std::array<uint8_t , 32> key {};
    DeriveLogKey( key , sid );
    CryptoPP::HMAC<CryptoPP::SHA256> hmac( key.data( ) , key.size( ) );

    if (sz % sizeof( LogRecV2 ) == 0) {
        size_t n = ( size_t ) ( sz / sizeof( LogRecV2 ) );
        std::vector<LogRecV2> buf( n );
        DWORD rd = 0; BOOL ok = ::ReadFile( h , buf.data( ) , ( DWORD ) ( n * sizeof( LogRecV2 ) ) , &rd , nullptr );
        ::CloseHandle( h );

        if (!ok) 
            return;

        for (const auto& r : buf) {
            if (r.tag != kLogTagV2)
                continue;

            std::array<uint8_t , 32> mac {}; hmac.Restart( );
            hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &r.tag ) , sizeof( r.tag ) );
            hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &r.seqNo ) , sizeof( r.seqNo ) );
            hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &r.maxSeen ) , sizeof( r.maxSeen ) );
            hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &r.daysAccrued ) , sizeof( r.daysAccrued ) );
            auto md5hex = Md5LowerHex( sid );
            hmac.Update( reinterpret_cast< const CryptoPP::byte* >( md5hex.data( ) ) , 8 * sizeof( wchar_t ) );
            hmac.Final( mac.data( ) );

            if (std::memcmp( mac.data( ) , r.mac , 16 ) != 0)
                continue;

            hasAuth = true;
            seqMax = ( std::max ) ( seqMax , r.seqNo );
            maxSeenMax = ( std::max ) ( maxSeenMax , r.maxSeen );
            daysMax = ( std::max ) ( daysMax , r.daysAccrued );
            }
        return;
        }

    if (sz % sizeof( LogRecV1 ) == 0) {
        size_t n = ( size_t ) ( sz / sizeof( LogRecV1 ) );
        std::vector<LogRecV1> buf( n );
        DWORD rd = 0; 
        BOOL ok = ::ReadFile( h , buf.data( ) , ( DWORD ) ( n * sizeof( LogRecV1 ) ) , &rd , nullptr );
        ::CloseHandle( h );
        if (!ok) 
            return;

        for (const auto& r : buf) {
            if (r.tag != kLogTagV1) 
                continue;

            seqMax = ( std::max ) ( seqMax , r.seqNo );
            maxSeenMax = ( std::max ) ( maxSeenMax , r.maxSeen );
            daysMax = ( std::max ) ( daysMax , r.daysAccrued );
            }
        return;
        }
    ::CloseHandle( h );
    }

/**
 * 鍔熻兘: 鍚戝悓涓€鎵瑰啓鍏ョ洰鏍囨枃浠跺拰鐩�鏍�(鍚� ADS)銆�
 * 鐢ㄦ硶: WriteAllTargets(bytes, targets);
 * 娉ㄦ剰: 閲囩敤 best-effort; 浠讳竴鎴愬姛鍗冲彲銆�
 */
static bool WriteAllTargets( const std::vector<uint8_t>& blob , const std::vector<Target>& t )
    {
    bool any = false;
    for (const auto& x : t) {
        std::wstring p = x.path + x.ads;
        HANDLE h = ::CreateFileW( p.c_str( ) , GENERIC_WRITE , FILE_SHARE_READ , nullptr ,
            CREATE_ALWAYS , FILE_ATTRIBUTE_HIDDEN | FILE_ATTRIBUTE_NOT_CONTENT_INDEXED , nullptr );

        if (h == INVALID_HANDLE_VALUE) 
            continue;

        DWORD wr = 0;
        BOOL ok = ::WriteFile( h , blob.data( ) , ( DWORD ) blob.size( ) , &wr , nullptr );
        ::FlushFileBuffers( h );
        ::CloseHandle( h );
        if (ok && wr == ( DWORD ) blob.size( )) 
            any = true;
        }
    return any;
    }

/**
 * 鍔熻兘: 璇诲彇鍗曚竴鐩�鏍�(涓绘枃浠舵垨 ADS)鍒板唴瀛樸€�
 * 鐢ㄦ硶: ReadOne(t, buf);
 * 娉ㄦ剰: 闄愬埗澶у皬 10MB; 澶辫触杩斿洖 false銆�
 */
static bool ReadOne( const Target& t , std::vector<uint8_t>& out )
    {
    std::wstring p = t.path + t.ads;
    HANDLE h = ::CreateFileW( p.c_str( ) , GENERIC_READ , FILE_SHARE_READ | FILE_SHARE_WRITE , nullptr ,
        OPEN_EXISTING , FILE_ATTRIBUTE_NORMAL , nullptr );

    if (h == INVALID_HANDLE_VALUE) 
        return false;

    DWORD sz = ::GetFileSize( h , nullptr );
    if (sz == INVALID_FILE_SIZE || sz == 0 || sz > kMaxBlobSize) {
        ::CloseHandle( h ); 
        return false; 
        }

    out.resize( sz );
    DWORD rd = 0; 
    BOOL ok = ::ReadFile( h , out.data( ) , sz , &rd , nullptr );
    ::CloseHandle( h );
    return ok && rd == sz;
    }

/**
 * 鍔熻兘: 閫氳繃澶氭暟鎶曠エ閫夋嫨鏈€鍙�闈犵殑鏂囨。; 鑻ユ棤澶氭暟鍒欓€夋嫨 seqNo 鏈€楂樸€�
 * 鐢ㄦ硶: size_t idx; MajoritySelect(list, idx);
 * 娉ㄦ剰: 绠€鍖栧�氭暟鍒ゅ畾(瀛楁�典竴鑷磋�板綍涓哄悓绁�)銆�
 */
static bool MajoritySelect( const std::vector<TrialDocV2>& docs , size_t& pick )
    {
    if (docs.empty( )) 
        return false;

    struct Key {
        uint64_t maxSeenUnix; uint32_t daysAccrued; uint32_t runsUsed; uint64_t installUnix; uint32_t seqNo;
        bool operator==( const Key& o ) const noexcept
            {
            return maxSeenUnix == o.maxSeenUnix && daysAccrued == o.daysAccrued &&
                runsUsed == o.runsUsed && installUnix == o.installUnix && seqNo == o.seqNo;
            }
        };

    struct H {
        size_t operator()( const Key& k ) const noexcept
            {
            auto h1 = std::hash<uint64_t> {}( k.maxSeenUnix );
            auto h2 = std::hash<uint32_t> {}( k.daysAccrued );
            auto h3 = std::hash<uint32_t> {}( k.runsUsed );
            auto h4 = std::hash<uint64_t> {}( k.installUnix );
            auto h5 = std::hash<uint32_t> {}( k.seqNo );
            return ( ( ( h1 ^ ( h2 << 1 ) ) ^ ( h3 << 1 ) ) ^ ( h4 << 1 ) ) ^ ( h5 << 1 );
            }
        };

    std::unordered_map<Key , std::pair<int , size_t> , H> cnt;

    for (size_t i = 0; i < docs.size( ); ++i) {
        const auto& d = docs [ i ];
        Key k { d.maxSeenUnix,d.daysAccrued,d.runsUsed,d.installUnix,d.seqNo };
        auto& e = cnt [ k ]; 
        if (e.first == 0)
            e.second = i; 
        e.first++;
        }

    int bestC = 0; 
    size_t bestIdx = 0; 
    uint32_t bestSeq = 0;

    for (auto& kv : cnt) {
        int c = kv.second.first; 
        size_t idx = kv.second.second; 
        uint32_t seq = docs [ idx ].seqNo;
        if (c > bestC || ( c == bestC && seq >= bestSeq )) { 
            bestC = c; 
            bestIdx = idx; 
            bestSeq = seq;
            }
        }
    pick = bestIdx; return true;
    }



/**
 * 鍔熻兘: 浠庢枃浠�+娉ㄥ唽琛ㄥ壇鏈�涓�璇诲彇鎵€鏈夊彲瑙ｅ瘑鏂囨。, 閫氳繃澶氭暟鎶曠エ绁ㄩ€夋渶浼樸€�
 * 鐢ㄦ硶: TrialDocV2 d; bool ok = LoadBestDoc(sid, md5, d);
 * 娉ㄦ剰: 澶辫触杩斿洖 false; 鏃犱换浣曟湁鏁堟枃妗ｃ€�
 */
static bool LoadBestDoc( const std::string& sid , const std::wstring& sidMd5Lower , TrialDocV2& out )
    {
    auto targets = BuildTargets( sidMd5Lower );
    std::vector<TrialDocV2> valid;

    for (auto& t : targets) {
        std::vector<uint8_t> blob;
        if (!ReadOne( t , blob )) 
            continue;

        TrialDocV2 d {};
        if (!DecryptDoc( blob , d , sid ))
            continue;

        if (d.version != kFileVersion)
            continue;

        valid.push_back( d );
        }
    // Registry replicas
    {
    std::vector<uint8_t> rblob; TrialDocV2 rd {};
    if (RegReadBlob2( sidMd5Lower , L"trial" , rblob )) {
        if (DecryptDoc( rblob , rd , sid ) && rd.version == kFileVersion)
            valid.push_back( rd );
        }

    rblob.clear( );

    if (RegReadBlob2( sidMd5Lower , L"alt" , rblob )) {
        if (DecryptDoc( rblob , rd , sid ) && rd.version == kFileVersion)
            valid.push_back( rd );
        }
    }

    if (valid.empty( )) 
        return false;

    size_t idx = 0; 
    MajoritySelect( valid , idx );
    out = valid [ idx ];
    return true;
    }

/**
 * 鍔熻兘: 灏嗘枃妗ｅ姞瀵嗗苟鍐欏叆鎵€鏈夊壇鏈�(鏂囦欢+娉ㄥ唽琛�), 鎴愬姛鍚庤拷鍔犳棩蹇椼€�
 * 鐢ㄦ硶: SaveDocAll(sid, md5, doc);
 * 娉ㄦ剰: 鑷冲皯浠讳竴鎴愬姛鎵嶈繑鍥� true; 澶辫触涓嶅啓鏃ュ織銆�
 */
static bool SaveDocAll( const std::string& sid , const std::wstring& sidMd5Lower , const TrialDocV2& doc )
    {
    std::vector<uint8_t> blob;
    if (!EncryptDoc( doc , blob , sid ))
        return false;

    auto targets = BuildTargets( sidMd5Lower );
    bool okFile = WriteAllTargets( blob , targets );
    bool okReg = RegWriteBoth( sidMd5Lower , blob );
    if (okFile || okReg) 
        LogAppend2( sidMd5Lower , doc , sid );

    return okFile || okReg;
    }

// ---- Named mutex for cross-process ----
/**
 * 鍔熻兘: 鑾峰彇杩涚▼鍛藉悕浜掓枼閿�(浼氳瘽绾� Local\HALVault_{md5})闃叉�㈠苟鍙戝啓鍏ャ€�
 * 鐢ㄦ硶: auto m = AcquireNamedMutex(md5);
 * 娉ㄦ剰: 绛夊緟 8s; 鑾峰彇澶辫触鏃惰嚜闄嶇骇(闄嶇骇, 浣嗕粛鑳芥搷浣�)銆�
 */
 /**
  * 鍔熻兘: 鑾峰彇杩涚▼鍛藉悕浜掓枼閿�(浼氳瘽绾� Local\HALVault_{md5})闃叉�㈠苟鍙戝啓鍏ャ€�
  * 鐢ㄦ硶: auto m = AcquireNamedMutex(md5);
  * 娉ㄦ剰: 绛夊緟 8s; 鑾峰彇澶辫触鏃惰嚜闄嶇骇(闄嶇骇, 浣嗕粛鑳芥搷浣�)銆�
  */
static UniqueHandle AcquireNamedMutex( const std::wstring& sidMd5Lower )
    {
    std::wstring name = L"Local\\HALVault_" + sidMd5Lower;
    HANDLE h = ::CreateMutexW( nullptr , FALSE , name.c_str( ) );
    if (!h) 
        return UniqueHandle {};

    DWORD wait = ::WaitForSingleObject( h , 8000 );
    if (wait == WAIT_OBJECT_0 || wait == WAIT_ABANDONED) {
        return make_unique_handle( h );
        }
    ::CloseHandle( h ); return UniqueHandle {};
    }


// ---- Core evaluation/update ----
static constexpr uint8_t kFlagMixed = 0x01; // reserved0 bit0

/** 瑙ｅ寘缁勫悎鍙傛暟涓鸿繍琛屽拰鏃ュ弬鏁� */
static inline void UnpackMixed( uint64_t packed , uint32_t& runsFirst , uint32_t& daysAfter )
    {
    runsFirst = ( uint32_t ) ( packed >> 32 );
    daysAfter = ( uint32_t ) ( packed & 0xFFFFFFFFu );
    }
/** 瑙ｅ寘缁勫悎鍙傛暟涓鸿繍琛屽拰鏃ュ弬鏁� */
static inline uint64_t PackMixed( uint32_t runsFirst , uint32_t daysAfter )
    {
    return ( uint64_t( runsFirst ) << 32 ) | uint64_t( daysAfter );
    }
/** 瀹夊叏绱�璁″ぉ鏁板苟鍔犳硶淇濇姢闃叉�㈠湪鏁板€兼椂淇�姝� lastDayIndex */
static inline void SafeAccrueDays( TrialDocV2& d , uint32_t today )
    {
    if (today > d.lastDayIndex) {
        uint32_t delta = today - d.lastDayIndex;
        uint32_t acc = d.daysAccrued;
        d.daysAccrued = ( UINT32_MAX - acc < delta ) ? UINT32_MAX : ( acc + delta );
        d.lastDayIndex = today;
        }
    }

/** 鍔犺浇鎴栧垵濮嬪寲鏂囨。锛岀己澶辨椂鍒濆�嬪寲绌烘枃妗� */
static int LoadOrInitDoc( TrialDocV2& d , uint64_t now , uint32_t today )
    {
    bool has = LoadBestDoc( g_sid , g_sidMd5Lower , d );
    if (has) 
        return HAL_OK;

    d.version = kFileVersion; 
    d.mode = ( uint8_t ) g_mode; 
    d.frozen = 0; 
    d.reserved0 = 0; 
    d.limit = g_limit;
    d.installUnix = now;
    d.maxSeenUnix = now;
    d.daysAccrued = 0; 
    d.lastDayIndex = today;
    d.runsUsed = 0; 
    d.seqNo = 1; 
    d.reserved1 = 0;
    if (!SaveDocAll( g_sid , g_sidMd5Lower , d )) { 
        HalSetLastError( HAL_E_IO ); return HAL_E_IO;
        }
    return HAL_OK;
    }

/** 璇诲彇鏈�鍦拌瘉鎹�鍒ゆ柇鍙�鑳� HMAC 浼�閫犳棩蹇楁敾鍑伙紝涓ラ噸鏃剁洿鎺ュ喕缁撹繑鍥� */
static int EvidenceGuard( TrialDocV2& d , uint64_t /*now*/ , uint32_t* remainingOut , uint32_t* totalOut )
    {
    uint32_t evSeq = 0 , evDays = 0;
    uint64_t evMax = 0;
    bool evAuth = false;
    LogReadEvidence2( g_sidMd5Lower , g_sid , evSeq , evMax , evDays , evAuth );
    bool strongTrigger = ( evMax > d.maxSeenUnix + kRollbackSlackSec ) || ( evSeq > d.seqNo + 3 );
    bool weakTrigger = ( evMax > d.maxSeenUnix + kRollbackSlackSec * 6 ) || ( evSeq > d.seqNo + 8 );

    if (( evAuth && strongTrigger ) || ( !evAuth && weakTrigger )) {
        d.frozen = 1;
        SaveDocAll( g_sid , g_sidMd5Lower , d );
        if (remainingOut) 
            *remainingOut = 0; 

        if (totalOut)
            *totalOut = d.limit;

        HalSetLastError( HAL_E_TRIAL_FROZEN );
        return HAL_E_TRIAL_FROZEN;
        }
    return HAL_OK;
    }

/** 浠呭熀浜�"绯荤粺鏃堕挓鍥炴挙"妫€鏌ュ拰鍐荤粨璺�寰勶紝鏃犳棩蹇楀己璇佹嵁鏃堕伩鍏嶈��鏉€ */
static int ClockOnlyRollbackCheck( TrialDocV2& d , uint64_t now , uint32_t* remainingOut , uint32_t* totalOut )
    {
    if (d.maxSeenUnix > now) {
        uint64_t diff = d.maxSeenUnix - now;
        if (diff > ( kRollbackSlackSec * 3ULL )) {
            d.frozen = 1; 
            SaveDocAll( g_sid , g_sidMd5Lower , d );

            if (remainingOut) 
                *remainingOut = 0; 

            if (totalOut) 
                *totalOut = d.limit;
            HalSetLastError( HAL_E_TRIAL_FROZEN ); return HAL_E_TRIAL_FROZEN;
            }
        }
    return HAL_OK;
    }

/** 鎺ㄨ繘鏃堕棿鍦板钩绾垮苟鍦ㄦ椂闂存寚閽堟椂鑷�鍔ㄨВ鍐� */
static inline void AdvanceHorizonAndThaw( TrialDocV2& d , uint64_t now )
    {
    if (now > d.maxSeenUnix) 
        d.maxSeenUnix = now;

    if (d.frozen && now >= d.maxSeenUnix) 
        d.frozen = 0;
    }

/** 璁＄畻閰嶉�濆苟鍦ㄩ渶瑕佹椂娑堣€椾竴涓� */
static void ComputeQuotaAndMaybeConsume( TrialDocV2& d , uint32_t today , bool consumeRun , uint32_t& remaining , uint32_t& totalLimit )
    {
    bool mixed = ( d.reserved0 & kFlagMixed ) != 0;
    uint32_t runsFirst = 0 , daysAfter = 0;
    if (mixed)
        UnpackMixed( d.reserved1 , runsFirst , daysAfter );

    if (mixed) {
        if (d.runsUsed < runsFirst) {
            // 鍏堣�＄畻鍓╀綑閲忥紙鍩轰簬褰撳墠鐘舵€侊級
            remaining = ( runsFirst > d.runsUsed ) ? ( runsFirst - d.runsUsed ) : 0;
            totalLimit = runsFirst;
             // 鍚庢秷鑰楋紙濡傛灉闇€瑕佷笖鏈夊墿浣欙級
            if (consumeRun && d.runsUsed < runsFirst && remaining > 0) 
                d.runsUsed += 1;
            }
        else {
            SafeAccrueDays( d , today );
            remaining = ( daysAfter > d.daysAccrued ) ? ( daysAfter - d.daysAccrued ) : 0;
            totalLimit = daysAfter;
            }
        return;
        }

    if (( TrialMode ) d.mode == TrialMode::Days) {
        SafeAccrueDays( d , today );
        remaining = ( d.limit > d.daysAccrued ) ? ( d.limit - d.daysAccrued ) : 0;
        totalLimit = d.limit;
        }
    else {
        // 鍏堣�＄畻鍓╀綑閲忥紙鍩轰簬褰撳墠鐘舵€侊級
        remaining = ( d.limit > d.runsUsed ) ? ( d.limit - d.runsUsed ) : 0;
        totalLimit = d.limit;
    
        // 鍚庢秷鑰楋紙濡傛灉闇€瑕佷笖鏈夊墿浣欙級
        if (consumeRun && d.runsUsed < d.limit && remaining > 0)
            d.runsUsed += 1;
        }
    }

/** 鏍稿績娴佺▼锛氫簰鏂ヨ幏鍙栭攣 鈫� 瑁呰浇/鍒濆�嬪寲 鈫� 璇佹嵁妫€鏌� 鈫� 鏃堕挓妫€鏌� 鈫� 鎺ㄨ繘/瑙ｅ喕 鈫� 閰嶉�濊�＄畻 鈫� 鎸佷箙鍖� */
static int CoreEvaluate( bool consumeRun , uint32_t* remainingOut , uint32_t* totalOut )
    {
    if (!g_inited) { 
        HalSetLastError( HAL_E_INVALIDARG ); 
        return HAL_E_INVALIDARG; 
        }

    auto mtx = AcquireNamedMutex( g_sidMd5Lower ); // 鍗充究鑾峰彇澶辫触涔熺户缁�锛屼緷璧栧�氭暟鎶曠エ瀹归敊

    TrialDocV2 d {};
    uint64_t now = UtcNowSec( );
    uint32_t today = DayIndex( now );

    {
    int rc = LoadOrInitDoc( d , now , today );
    if (rc != HAL_OK)
        return rc;
    }

    // 浣跨敤灞€閮ㄥ彉閲忔壙鎺ュ畨鍏ㄦ�€鏌ョ殑杈撳嚭锛岄伩鍏嶆薄鏌撴渶缁堣緭鍑哄弬鏁�
    uint32_t guardRemaining = 0 , guardTotal = 0;

    {
    int rc = EvidenceGuard( d , now , &guardRemaining , &guardTotal );
    if (rc != HAL_OK) {
        if (remainingOut)
            *remainingOut = guardRemaining;

        if (totalOut)     
            *totalOut     = guardTotal;
        return rc; // 宸插喕缁撹繑鍥�
        }
    }

    {
    int rc = ClockOnlyRollbackCheck( d , now , &guardRemaining , &guardTotal );
    if (rc != HAL_OK) {
        if (remainingOut) 
            *remainingOut = guardRemaining;

        if (totalOut)    
            *totalOut     = guardTotal;
        return rc; // 宸插喕缁撹繑鍥�
        }
    }

    AdvanceHorizonAndThaw( d , now );

    // 蹇�鐓х敤浜庡垽瀹氭槸鍚﹂渶瑕佹寔涔呭寲
    TrialDocV2 before = d;

    uint32_t remaining = 0 , totalLimit = 0;
    ComputeQuotaAndMaybeConsume( d , today , consumeRun , remaining , totalLimit );

    // 浠呭綋鏂囨。鐘舵€佺湡姝ｅ彂鐢熷彉鍖栨椂鎵� bump 搴忓彿骞朵繚瀛�
    auto doc_changed = []( const TrialDocV2& a , const TrialDocV2& b ) noexcept
        {
        return a.mode         != b.mode
            || a.frozen       != b.frozen
            || a.limit        != b.limit
            || a.installUnix  != b.installUnix
            || a.maxSeenUnix  != b.maxSeenUnix
            || a.daysAccrued  != b.daysAccrued
            || a.lastDayIndex != b.lastDayIndex
            || a.runsUsed     != b.runsUsed
            || a.reserved0    != b.reserved0
            || a.reserved1    != b.reserved1;
        };

    bool mutated = doc_changed( before , d );

    if (mutated) {
        d.seqNo += 1;
        if (!SaveDocAll( g_sid , g_sidMd5Lower , d )) { 
            HalSetLastError( HAL_E_IO ); 
            return HAL_E_IO; 
            }
        }

    if (remainingOut) 
        *remainingOut = remaining;

    if (totalOut)
        *totalOut = totalLimit;

    if (d.frozen) { 
        HalSetLastError( HAL_E_TRIAL_FROZEN );
        return HAL_E_TRIAL_FROZEN; 
        }

    if (remaining == 0) {
        HalSetLastError( HAL_E_TRIAL_EXPIRED ); 
        return HAL_E_TRIAL_EXPIRED;
        }
    HalSetLastError( HAL_OK );
    return HAL_OK;
    }

// =====================================================================================
//                                      API
// =====================================================================================

/**
 * 鍔熻兘: 鍒濆�嬪寲璇曠敤绯荤粺(璁剧疆杞�浠禝D骞舵淳鐢熺瓥鐣�)銆�
 * 鐢ㄦ硶: TrialInitialize("MCE0012");
 * 娉ㄦ剰: 绾跨▼瀹夊叏; 浼氳Е鍙戜竴娆� CoreEvaluate 浠ョ‘淇濊瘯鐢ㄥ瓨鍦ㄣ€�
 */
extern "C" HALVAULT_API int TrialInitialize( const char* softwareId ) noexcept
    {
    VMP_GUARD( "TrialInitialize" );
    if (!softwareId) {
        HalSetLastError( HAL_E_INVALIDARG );
        return HAL_E_INVALIDARG;
        }

    std::lock_guard<std::mutex> lk( g_mutex );
    g_sid = softwareId;
   
    g_sid.assign( softwareId );
    std::transform( g_sid.begin( ) , g_sid.end( ) , g_sid.begin( ) , ::tolower );

    // --- white-box derive policy ---
    DerivePolicy( g_sid , g_mode , g_limit );

    g_sidMd5Lower = Md5LowerHex( g_sid );
    g_inited = true;

    // Force-create doc if none
    uint32_t rem = 0; 
    return CoreEvaluate( false , &rem , nullptr );
    }

/**
 * 鍔熻兘: 鏌ヨ�㈠綋鍓嶅墿浣欓厤棰濇暟閲�(涓嶆秷鑰�)銆�
 * 鐢ㄦ硶: TrialEvaluate(&remain, &total);
 * 娉ㄦ剰: 杩斿洖鎴愬姛/閿欒��, 閫氳繃杩斿洖鍊煎拰 HalSetLastError 浼犻€掋€�
 */
extern "C" HALVAULT_API int TrialEvaluate( uint32_t * remainingOut ,
    uint32_t * totalLimitOut ) noexcept
    {
    VMP_GUARD( "TrialEvaluate" );
    return CoreEvaluate( false , remainingOut , totalLimitOut );
    }

/**
 * 鍔熻兘: 鍦ㄣ€屾寜娆￠樁娈点€�/妯″紡涓�娑堣€椾竴娆¤繍琛屻€�
 * 鐢ㄦ硶: TrialConsumeOneRun(&remain, &total);
 * 娉ㄦ剰: 鍦ㄣ€屾寜澶┿€嶆ā寮忎笅涓嶆秷鑰� days; 娣峰悎绛栫暐闃舵��1鎵嶆湁娆℃暟娑堣€椼€�
 */
extern "C" HALVAULT_API int TrialConsumeOneRun( uint32_t * remainingOut ,
    uint32_t * totalLimitOut ) noexcept
    {
    VMP_GUARD( "TrialConsumeOneRun" );
    return CoreEvaluate( true , remainingOut , totalLimitOut );
    }

/**
 * 鍔熻兘: 鑾峰彇瀹屾暣鐨勮瘯鐢ㄧ姸鎬佷俊鎭�銆�
 * 鐢ㄦ硶: TrialStatus st; TrialGetStatus(&st);
 * 娉ㄦ剰: 杩斿洖鎵€鏈夎瘯鐢ㄧ浉鍏充俊鎭�锛屽寘鎷�妯″紡銆佸墿浣欍€佸凡鐢ㄧ瓑銆�
 */
extern "C" HALVAULT_API int TrialGetStatus( TrialStatus* statusOut ) noexcept
    {
    VMP_GUARD( "TrialGetStatus" );
    if (!statusOut || !g_inited) {
        HalSetLastError( HAL_E_INVALIDARG );
        return HAL_E_INVALIDARG;
        }

    // 鑾峰彇鍩烘湰淇℃伅
    uint32_t remaining = 0, total = 0;
    int rc = CoreEvaluate( false , &remaining , &total );
    
    // 濉�鍏呯姸鎬佺粨鏋�
    statusOut->allowed = (rc == 0 && remaining > 0);
    statusOut->remaining = remaining;
    statusOut->limit = total;
    statusOut->mode = g_mode;
    statusOut->nowUnix = static_cast<uint32_t>(UtcNowSec());
    
    // 鑾峰彇璇︾粏淇℃伅锛堥渶瑕佽�诲彇璇曠敤鏂囨。锛�
    TrialDocV2 doc;
    bool hasDoc = LoadBestDoc( g_sid , g_sidMd5Lower , doc );
    if (hasDoc) {
        statusOut->runsUsed = doc.runsUsed;
        statusOut->installUnix = static_cast<uint32_t>(doc.installUnix);
        } else {
        statusOut->runsUsed = 0;
        statusOut->installUnix = statusOut->nowUnix;
        }
    
    return rc;
    }

// Default policy by softwareId prefix (adjust rules to your fleet)
/**
 * 鍔熻兘: 鏍规嵁杞�浠禝D鍓嶇紑鑾峰彇榛樿�ょ瓥鐣ラ厤缃�銆�
 * 鐢ㄦ硶: TrialGetDefaultConfig(sid, mode, limit);
 * 娉ㄦ剰: 鍙�鎸変骇鍝佽嚜瀹氫箟鍓嶇紑瑙勫垯; 鏈�鍖归厤鏃堕粯璁� 14 澶┿€�
 */
HALVAULT_API int TrialGetDefaultConfig( const char* softwareId ,
    TrialMode& modeOut ,
    uint32_t& limitOut ) noexcept
    {
    VMP_GUARD( "TrialGetDefaultConfig" );
    if (!softwareId) {
        modeOut = TrialMode::Days;
        limitOut = 14;
        HalSetLastError( HAL_OK );
        return HAL_OK;
        }

    std::string sid( softwareId );
    DerivePolicy( sid , modeOut , limitOut );
    HalSetLastError( HAL_OK );
    return HAL_OK;
    }

// ---- Policy derivation (white-box mapping) ---------------------------------
struct PolicyEntry { std::array<uint8_t , 32> hash; uint8_t mode; uint16_t limit; };
static std::vector<PolicyEntry> g_policyTable;
static bool InitPolicyTable( ) noexcept
    {
    if (!g_policyTable.empty( )) return true;
    
    try {
        // Verify HMAC first for integrity
        CryptoPP::HMAC<CryptoPP::SHA256> hmac( kPolicyKey , sizeof( kPolicyKey ) );
        hmac.Update( kPolicyBlob , kPolicyBlobSize );
        hmac.Update( reinterpret_cast< const CryptoPP::byte* >( &POLICY_VERSION ) , sizeof( POLICY_VERSION ) );
        
        std::array<uint8_t , 32> computedHmac {};
        hmac.Final( computedHmac.data( ) );
        
        if (std::memcmp( computedHmac.data( ) , kPolicyHmac , 32 ) != 0) {
            return false; // integrity check failed
            }
            
        // Decrypt policy blob using AES-CBC
        CryptoPP::CBC_Mode<CryptoPP::AES>::Decryption dec;
        dec.SetKeyWithIV( kPolicyKey , sizeof( kPolicyKey ) , kPolicyIV , sizeof( kPolicyIV ) );
        
        std::string decrypted;
        CryptoPP::StringSource ss( kPolicyBlob , kPolicyBlobSize , true ,
            new CryptoPP::StreamTransformationFilter( dec ,
                new CryptoPP::StringSink( decrypted ) ) );
                
        // Parse decrypted data: each entry is 35 bytes (32 hash + 1 mode + 2 limit)
        if (decrypted.size( ) % 35 != 0) {
            return false; // invalid data format
            }
            
        size_t numEntries = decrypted.size( ) / 35;
        g_policyTable.reserve( numEntries );
        
        const uint8_t* data = reinterpret_cast< const uint8_t* >( decrypted.data( ) );
        for (size_t i = 0; i < numEntries; ++i) {
            PolicyEntry entry {};
            
            // Copy 32-byte hash
            std::memcpy( entry.hash.data( ) , data + i * 35 , 32 );
            
            // Extract mode (1 byte)
            entry.mode = data [ i * 35 + 32 ];
            
            // Extract limit (2 bytes, little endian)
            entry.limit = static_cast< uint16_t >( data [ i * 35 + 33 ] ) |
                         ( static_cast< uint16_t >( data [ i * 35 + 34 ] ) << 8 );
                         
            g_policyTable.push_back( entry );
            }
            
        return true;
        }
    catch (...) {
        g_policyTable.clear( );
        return false;
        }
    }
static constexpr uint8_t kLimitBaseDays = 7;   // 鏈€灏忓ぉ鏁�
static constexpr uint8_t kLimitRangeDays = 15;  // +0~14 => 7~21
static constexpr uint8_t kLimitBaseRuns = 10;  // 鏈€灏忔�℃暟
static constexpr uint8_t kLimitRangeRuns = 21;  // +0~20 => 10~30
static constexpr char kSecretSalt [ ] = "HV7s9e@X!qU*1#4P";

static void DerivePolicy( const std::string& sid , TrialMode& mode , uint32_t& limit ) noexcept
    {
    VMP_GUARD( "DerivePolicy" );
    // Convert to lowercase to match PolicyGen.py behavior
    std::string sid_lower = sid;
    std::transform(sid_lower.begin(), sid_lower.end(), sid_lower.begin(), ::tolower);
    
    std::array<uint8_t , 32> h {};
    CryptoPP::SHA256 sha; 
    sha.CalculateDigest( h.data( ) , ( const uint8_t* ) sid_lower.c_str( ) , sid_lower.size( ) );

    if (InitPolicyTable( )) {
        for (const auto& e : g_policyTable) {
            if (std::memcmp( e.hash.data( ) , h.data( ) , 32 ) == 0) {
                mode = ( TrialMode ) e.mode; 
                limit = e.limit; return;
                }
            }
        }
    // fallback random mapping
    CryptoPP::SHA256 sha2; 
    std::array<uint8_t , 32> dig {}; 
    sha2.CalculateDigest( dig.data( ) , ( const uint8_t* ) sid.data( ) , sid.size( ) );
    mode = ( dig [ 0 ] & 1 ) ? TrialMode::Runs : TrialMode::Days;
    if (mode == TrialMode::Days) {
        limit = kLimitBaseDays + ( dig [ 1 ] % kLimitRangeDays );
        }
    else {
        limit = kLimitBaseRuns + ( dig [ 2 ] % kLimitRangeRuns );
        }
    }

// Optional: enable mixed trial policy (phase 1: runs, then fallback to days)
/**
 * 鍔熻兘: 璁剧疆銆屾贩鍚堢瓥鐣ャ€�: 鍏堢粰 runsFirst 娆�, 鐢ㄥ畬鍚庡啀缁� daysAfter 澶┿€�
 * 鐢ㄦ硶: TrialConfigureMixed(5, 7);
 * 娉ㄦ剰: 闇€鍦� TrialInitialize 涔嬪悗; 鑻ュ凡瀛樺湪鏂囨。鍒欎粎鏇存柊閰嶇疆骞� bump seqNo銆�
 */
extern "C" HALVAULT_API int TrialConfigureMixed( uint32_t runsFirst , uint32_t daysAfter ) noexcept
    {
    VMP_GUARD( "TrialConfigureMixed" );
    if (!g_inited) {
        HalSetLastError( HAL_E_INVALIDARG );
        return HAL_E_INVALIDARG;
        }

    auto mtx = AcquireNamedMutex( g_sidMd5Lower );
    TrialDocV2 d {};
    bool has = LoadBestDoc( g_sid , g_sidMd5Lower , d );

    if (!has) {
        // initialize minimal doc
        uint64_t now = UtcNowSec( );
        d.version = kFileVersion;
        d.mode = ( uint8_t ) g_mode;
        d.frozen = 0;
        d.reserved0 = 0;
        d.limit = g_limit;
        d.installUnix = now;
        d.maxSeenUnix = now;
        d.daysAccrued = 0;
        d.lastDayIndex = DayIndex( now );
        d.runsUsed = 0;
        d.seqNo = 1;
        d.reserved1 = 0;
        }
    d.reserved0 |= kFlagMixed;
    d.reserved1 = PackMixed( runsFirst , daysAfter );
    d.seqNo += 1;
    if (!SaveDocAll( g_sid , g_sidMd5Lower , d )) {
        HalSetLastError( HAL_E_IO );
        return HAL_E_IO;
        }
    HalSetLastError( HAL_OK ); return HAL_OK;
    }

// =====================================================================================
//                              ADAPTER / MIGRATION NOTES
// =====================================================================================
// 1) If your header declares different function names or extra parameters,
//    wrap them to call TrialInitialize / TrialEvaluate / TrialConsumeOneRun.
// 2) Migration from your old v1 container:
//    - (Optional) Define HALVAULT_ENABLE_V1_MIGRATION and implement DecryptDocV1() below.
//      At load time, if no v2 replicas are found, attempt to read legacy v1, convert to v2,
//      

namespace halvault
    {
    // 鍒濆�嬪寲璇曠敤绯荤粺
    int Initialize( const std::string& softwareId ) noexcept
        {
        return ::TrialInitialize( softwareId.c_str( ) );
        }

    // 鑾峰彇瀹屾暣璇曠敤鐘舵€侊紙鎺ㄨ崘锛�
    int GetStatus( TrialStatus& status ) noexcept
        {
        return ::TrialGetStatus( &status );
        }

    // 娑堣€椾竴娆¤繍琛岋紙浠呭湪Runs妯″紡鎴朚ixed妯″紡闃舵��1鏈夋晥锛�
    int ConsumeOneRun( TrialStatus& status ) noexcept
        {
        VMP_GUARD( "ConsumeOneRun" );
        int rc = ::TrialConsumeOneRun( nullptr , nullptr );
        if (rc == 0) {
            // 娑堣€楁垚鍔熷悗鏇存柊鐘舵€�
            rc = ::TrialGetStatus( &status );
            }
        return rc;
        }


    // 鑾峰彇閰嶇疆淇℃伅
    int GetConfig( const std::string& softwareId , TrialMode& mode , uint32_t& limit ) noexcept
        {
        return ::TrialGetDefaultConfig( softwareId.c_str( ) , mode , limit );
        }

    // 渚垮埄鍑芥暟锛氭�€鏌ヨ瘯鐢ㄦ槸鍚︽湁鏁�
    bool IsTrialValid( ) noexcept
        {
        VMP_GUARD( "IsTrialValid" );
        TrialStatus status;
        int rc = ::TrialGetStatus( &status );
        return ( rc == 0 ) && status.allowed && ( status.remaining > 0 );
        }

    // 渚垮埄鍑芥暟锛氳幏鍙栧墿浣欓厤棰�
    uint32_t GetRemainingQuota( ) noexcept
        {
        VMP_GUARD( "GetRemainingQuota" );
        TrialStatus status;
        int rc = ::TrialGetStatus( &status );
        return ( rc == 0 ) ? status.remaining : 0;
        }

    } // namespace halvault